/* AI搜索调研助手自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 搜索结果样式 */
.search-result-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: white;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-result-title {
    color: #0d6efd;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
}

.search-result-title:hover {
    color: #0a58ca;
    text-decoration: underline;
}

.search-result-url {
    color: #198754;
    font-size: 0.9rem;
    word-break: break-all;
}

.search-result-snippet {
    color: #6c757d;
    margin-top: 8px;
    line-height: 1.5;
}

/* AI分析内容样式 */
.analysis-content {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #0d6efd;
    white-space: pre-wrap;
    line-height: 1.8;
}

.analysis-content h1,
.analysis-content h2,
.analysis-content h3,
.analysis-content h4,
.analysis-content h5,
.analysis-content h6 {
    color: #0d6efd;
    margin-top: 20px;
    margin-bottom: 10px;
}

.analysis-content ul,
.analysis-content ol {
    margin-left: 20px;
}

.analysis-content li {
    margin-bottom: 5px;
}

/* URL选择列表样式 */
.url-selection-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    background-color: white;
}

.url-selection-item:hover {
    background-color: #f8f9fa;
}

.url-selection-item .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 深度分析结果样式 */
.deep-analysis-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    background-color: white;
}

.deep-analysis-item.success {
    border-left: 4px solid #198754;
}

.deep-analysis-item.failed {
    border-left: 4px solid #dc3545;
}

.deep-analysis-url {
    color: #198754;
    font-weight: 600;
    font-size: 0.9rem;
    word-break: break-all;
    margin-bottom: 10px;
}

.deep-analysis-content {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    white-space: pre-wrap;
    line-height: 1.6;
}

/* 加载动画样式 */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 消息提示样式 */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .search-result-item {
        padding: 12px;
    }
    
    .analysis-content {
        padding: 15px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-success {
    background-color: #198754;
}

.status-failed {
    background-color: #dc3545;
}

.status-loading {
    background-color: #ffc107;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}
