# 部署指南

本文档详细说明如何在不同环境中部署AI搜索调研助手应用。

## 部署环境要求

### 系统要求
- **操作系统**：Windows 10+, Ubuntu 18.04+, CentOS 7+, macOS 10.14+
- **Python版本**：3.8 或更高版本
- **内存**：建议 2GB 以上
- **存储空间**：建议 1GB 以上
- **网络**：需要访问外部API（OpenAI、Google搜索）

### 依赖服务
- **OpenAI API**：需要有效的API密钥
- **Google搜索API**：内部搜索服务地址
- **Chrome浏览器**：用于网页抓取（DrissionPage需要）

## 开发环境部署

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd ai_research_tool

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 升级pip
python -m pip install --upgrade pip
```

### 2. 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 验证安装
python -c "import flask, openai, DrissionPage; print('依赖安装成功')"
```

### 3. 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
# Windows
notepad .env
# Linux/macOS
nano .env
```

配置示例：
```env
# 必需配置
OPENAI_API_KEY=sk-your-openai-api-key
GOOGLE_SEARCH_API_URL=http://***********:18183/search

# 开发环境配置
FLASK_ENV=development
FLASK_DEBUG=True
LOG_LEVEL=DEBUG
```

### 4. 启动应用
```bash
# 启动开发服务器
python app.py

# 或使用Flask命令
export FLASK_APP=app.py
flask run --host=0.0.0.0 --port=5000
```

### 5. 验证部署
访问 http://localhost:5000 确认应用正常运行。

## 生产环境部署

### 使用Gunicorn部署

#### 1. 安装Gunicorn
```bash
pip install gunicorn
```

#### 2. 创建Gunicorn配置文件
创建 `gunicorn.conf.py`：
```python
# Gunicorn配置文件
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

#### 3. 启动Gunicorn
```bash
gunicorn -c gunicorn.conf.py app:app
```

### 使用Nginx反向代理

#### 1. 安装Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 2. 配置Nginx
创建 `/etc/nginx/sites-available/ai_research_tool`：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /static {
        alias /path/to/ai_research_tool/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 3. 启用站点
```bash
sudo ln -s /etc/nginx/sites-available/ai_research_tool /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 使用Systemd服务

#### 1. 创建服务文件
创建 `/etc/systemd/system/ai_research_tool.service`：
```ini
[Unit]
Description=AI Research Tool
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/ai_research_tool
Environment=PATH=/path/to/ai_research_tool/venv/bin
ExecStart=/path/to/ai_research_tool/venv/bin/gunicorn -c gunicorn.conf.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

#### 2. 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable ai_research_tool
sudo systemctl start ai_research_tool
sudo systemctl status ai_research_tool
```

## Docker部署

### 1. 创建Dockerfile
```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Chrome
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "-c", "gunicorn.conf.py", "app:app"]
```

### 2. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  ai_research_tool:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_SEARCH_API_URL=${GOOGLE_SEARCH_API_URL}
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./static:/app/static
    depends_on:
      - ai_research_tool
    restart: unless-stopped
```

### 3. 构建和运行
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 环境配置

### 生产环境配置
```env
# 生产环境配置
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-production-secret-key

# API配置
OPENAI_API_KEY=your-openai-api-key
GOOGLE_SEARCH_API_URL=http://***********:18183/search

# 性能配置
SCRAPING_TIMEOUT=60
MAX_RETRIES=5
REQUEST_DELAY=2

# 日志配置
LOG_LEVEL=WARNING
LOG_FILE=logs/app.log
```

### 安全配置
1. **API密钥安全**
   - 使用环境变量存储敏感信息
   - 定期轮换API密钥
   - 限制API密钥权限

2. **网络安全**
   - 使用HTTPS
   - 配置防火墙
   - 限制访问IP

3. **应用安全**
   - 设置强密码的SECRET_KEY
   - 启用CSRF保护
   - 配置适当的CORS策略

## 监控和维护

### 日志监控
```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log

# 日志轮转配置
sudo logrotate -d /etc/logrotate.d/ai_research_tool
```

### 性能监控
- 监控CPU和内存使用率
- 监控API响应时间
- 监控错误率和成功率

### 备份策略
- 定期备份配置文件
- 备份日志文件
- 备份应用代码

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查Python版本和依赖
   - 验证配置文件
   - 查看错误日志

2. **API调用失败**
   - 检查网络连接
   - 验证API密钥
   - 检查API配额

3. **网页抓取失败**
   - 确认Chrome浏览器已安装
   - 检查目标网站访问权限
   - 调整超时设置

4. **性能问题**
   - 增加worker数量
   - 优化数据库查询
   - 启用缓存

### 调试命令
```bash
# 检查服务状态
sudo systemctl status ai_research_tool

# 查看端口占用
netstat -tlnp | grep :5000

# 测试API连接
curl -X GET http://localhost:5000/api/health

# 检查依赖版本
pip list | grep -E "(flask|openai|DrissionPage)"
```

## 更新和升级

### 应用更新
```bash
# 停止服务
sudo systemctl stop ai_research_tool

# 更新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt

# 重启服务
sudo systemctl start ai_research_tool
```

### 数据库迁移
如果未来添加数据库功能，需要考虑数据迁移策略。

### 版本回滚
```bash
# 回滚到上一个版本
git checkout HEAD~1

# 重启服务
sudo systemctl restart ai_research_tool
```

## 扩展部署

### 负载均衡
使用多个应用实例和负载均衡器提高可用性：

```nginx
upstream ai_research_tool {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001;
    server 127.0.0.1:5002;
}

server {
    listen 80;
    location / {
        proxy_pass http://ai_research_tool;
    }
}
```

### 容器编排
使用Kubernetes进行大规模部署：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-research-tool
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-research-tool
  template:
    metadata:
      labels:
        app: ai-research-tool
    spec:
      containers:
      - name: ai-research-tool
        image: ai-research-tool:latest
        ports:
        - containerPort: 5000
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: openai-key
```

这样就完成了完整的部署指南，涵盖了从开发环境到生产环境的各种部署方式。
