"""
数据库模型定义
定义搜索历史等数据表结构
"""
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text

db = SQLAlchemy()

class SearchHistory(db.Model):
    """搜索历史记录模型"""
    
    __tablename__ = 'search_history'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    keywords = db.Column(db.String(500), nullable=False, comment='搜索关键词')
    custom_prompt = db.Column(db.Text, nullable=True, comment='自定义提示词')
    search_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, comment='搜索时间')
    result_count = db.Column(db.Integer, nullable=False, default=0, comment='搜索结果数量')
    ip_address = db.Column(db.String(45), nullable=True, comment='用户IP地址')
    user_agent = db.Column(db.String(500), nullable=True, comment='用户代理')
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<SearchHistory {self.id}: {self.keywords}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'keywords': self.keywords,
            'custom_prompt': self.custom_prompt,
            'search_time': self.search_time.isoformat() if self.search_time else None,
            'result_count': self.result_count,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def create_record(cls, keywords, custom_prompt=None, result_count=0, ip_address=None, user_agent=None):
        """创建搜索历史记录"""
        record = cls(
            keywords=keywords,
            custom_prompt=custom_prompt,
            result_count=result_count,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.session.add(record)
        db.session.commit()
        return record
    
    @classmethod
    def get_recent_history(cls, limit=50, offset=0):
        """获取最近的搜索历史"""
        return cls.query.order_by(cls.search_time.desc()).offset(offset).limit(limit).all()
    
    @classmethod
    def search_history(cls, keyword, limit=50):
        """搜索历史记录"""
        return cls.query.filter(
            cls.keywords.contains(keyword)
        ).order_by(cls.search_time.desc()).limit(limit).all()
    
    @classmethod
    def delete_record(cls, record_id):
        """删除指定记录"""
        record = cls.query.get(record_id)
        if record:
            db.session.delete(record)
            db.session.commit()
            return True
        return False
    
    @classmethod
    def clear_all_history(cls):
        """清空所有历史记录"""
        count = cls.query.count()
        cls.query.delete()
        db.session.commit()
        return count
    
    @classmethod
    def get_statistics(cls):
        """获取统计信息"""
        total_count = cls.query.count()
        today_count = cls.query.filter(
            db.func.date(cls.search_time) == db.func.date(datetime.utcnow())
        ).count()
        
        return {
            'total_searches': total_count,
            'today_searches': today_count
        }

def init_database(app):
    """初始化数据库"""
    with app.app_context():
        try:
            # 检查数据库连接
            db.session.execute(text('SELECT 1'))

            # 创建所有表
            db.create_all()

            # 检查表是否存在
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()

            if 'search_history' in tables:
                app.logger.info("数据库表已存在或创建成功")
            else:
                app.logger.warning("搜索历史表创建失败")

        except Exception as e:
            app.logger.error(f"数据库初始化失败: {str(e)}")

            # 如果是MySQL连接失败，尝试使用SQLite作为备选
            if 'mysql' in str(e).lower() or 'pymysql' in str(e).lower():
                app.logger.warning("MySQL连接失败，切换到SQLite数据库")
                try:
                    # 切换到SQLite数据库
                    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ai_research_tool.db'

                    # 重新初始化数据库
                    db.init_app(app)
                    db.create_all()

                    app.logger.info("SQLite数据库初始化成功")
                    return

                except Exception as sqlite_error:
                    app.logger.error(f"SQLite数据库初始化失败: {str(sqlite_error)}")

            # 如果是MySQL且数据库不存在，尝试创建
            try:
                if 'mysql' in app.config['SQLALCHEMY_DATABASE_URI']:
                    create_database_if_not_exists(app)
                    db.create_all()
                    app.logger.info("MySQL数据库和表创建成功")
                else:
                    raise e
            except Exception as create_error:
                app.logger.error(f"数据库创建失败: {str(create_error)}")
                app.logger.warning("数据库功能将不可用，搜索历史功能将被禁用")

def create_database_if_not_exists(app):
    """如果数据库不存在则创建"""
    import pymysql
    
    config = app.config
    
    # 连接MySQL服务器（不指定数据库）
    connection = pymysql.connect(
        host=config['MYSQL_HOST'],
        port=config['MYSQL_PORT'],
        user=config['MYSQL_USER'],
        password=config['MYSQL_PASSWORD'],
        charset='utf8mb4'
    )
    
    try:
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{config['MYSQL_DATABASE']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            app.logger.info(f"数据库 {config['MYSQL_DATABASE']} 创建成功或已存在")
    finally:
        connection.close()
