"""
Google搜索服务模块
集成内部Google搜索API，提供搜索功能
"""
import requests
import time
from typing import List, Dict, Optional
from flask import current_app
from app.utils.logger import get_logger

logger = get_logger(__name__)

class SearchService:
    """Google搜索服务类"""
    
    def __init__(self):
        self.api_url = current_app.config['GOOGLE_SEARCH_API_URL']
        self.max_retries = current_app.config['MAX_RETRIES']
        self.request_delay = current_app.config['REQUEST_DELAY']
        self.timeout = current_app.config['SCRAPING_TIMEOUT']
    
    def search(self, keywords: str, num_results: int = 10) -> List[Dict]:
        """
        执行搜索

        Args:
            keywords: 搜索关键词
            num_results: 返回结果数量

        Returns:
            搜索结果列表
        """
        logger.info(f"开始搜索: {keywords}")

        for attempt in range(self.max_retries):
            try:
                # 构建请求参数，适配新的API格式
                params = {
                    'q': keywords,
                    'c2coff': '0',
                    'cr': '',
                    'dateRestrict': '',
                    'exactTerms': '',
                    'excludeTerms': '',
                    'fileType': '',
                    'filter': '1',
                    'gl': '',
                    'hl': '',
                    'num': str(num_results),
                    'format': 'json'
                }

                logger.debug(f"搜索请求参数: {params}")

                # 发送请求
                response = requests.get(
                    self.api_url,
                    params=params,
                    timeout=self.timeout,
                    headers={
                        'User-Agent': 'AI-Research-Tool/1.0',
                        'Accept': 'application/json'
                    }
                )
                
                # 检查响应状态
                response.raise_for_status()
                print(response.text)
                # 解析响应
                data = response.json()
                results = self._parse_search_results(data)
                
                logger.info(f"搜索成功: {keywords}, 返回 {len(results)} 个结果")
                return results
                
            except requests.exceptions.Timeout:
                logger.warning(f"搜索请求超时 (尝试 {attempt + 1}/{self.max_retries}): {keywords}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception("搜索请求超时，已达到最大重试次数")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"搜索请求失败 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception(f"搜索请求失败: {str(e)}")
                    
            except ValueError as e:
                logger.error(f"搜索结果解析失败: {str(e)}")
                raise Exception(f"搜索结果解析失败: {str(e)}")
                
            except Exception as e:
                logger.error(f"搜索过程中发生未知错误: {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception(f"搜索失败: {str(e)}")
        
        return []
    
    def _parse_search_results(self, data: Dict) -> List[Dict]:
        """
        解析搜索结果
        
        Args:
            data: API返回的原始数据
            
        Returns:
            格式化的搜索结果列表
        """
        results = []
        
        try:
            # 根据API返回格式解析结果
            # 假设API返回格式为: {"results": [{"title": "", "url": "", "snippet": ""}]}
            if 'results' in data:
                for item in data['results']:
                    result = {
                        'title': item.get('title', ''),
                        'url': item.get('url', ''),
                        'snippet': item.get('snippet', ''),
                        'display_url': item.get('display_url', item.get('url', ''))
                    }
                    results.append(result)
            
            # 如果API返回格式不同，可能需要调整解析逻辑
            elif 'items' in data:
                for item in data['items']:
                    result = {
                        'title': item.get('title', ''),
                        'url': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'display_url': item.get('displayLink', item.get('link', ''))
                    }
                    results.append(result)
            
            # 处理其他可能的格式
            elif isinstance(data, list):
                for item in data:
                    if isinstance(item, dict):
                        result = {
                            'title': item.get('title', ''),
                            'url': item.get('url', item.get('link', '')),
                            'snippet': item.get('snippet', item.get('description', '')),
                            'display_url': item.get('display_url', item.get('url', item.get('link', '')))
                        }
                        results.append(result)
            
            logger.debug(f"解析得到 {len(results)} 个搜索结果")
            
        except Exception as e:
            logger.error(f"解析搜索结果时发生错误: {str(e)}")
            logger.debug(f"原始数据: {data}")
            raise ValueError(f"无法解析搜索结果: {str(e)}")
        
        return results
    
    def validate_search_results(self, results: List[Dict]) -> List[Dict]:
        """
        验证和清理搜索结果
        
        Args:
            results: 原始搜索结果
            
        Returns:
            验证后的搜索结果
        """
        validated_results = []
        
        for result in results:
            # 检查必要字段
            if result.get('url') and result.get('title'):
                # 清理和验证URL
                url = result['url'].strip()
                if url.startswith('http://') or url.startswith('https://'):
                    validated_results.append(result)
                else:
                    logger.warning(f"跳过无效URL: {url}")
            else:
                logger.warning(f"跳过不完整的搜索结果: {result}")
        
        logger.info(f"验证后保留 {len(validated_results)} 个有效结果")
        return validated_results
