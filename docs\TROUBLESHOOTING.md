# 故障排除指南

## 常见问题及解决方案

### 1. TemplateNotFound 错误

**问题描述：**
```
jinja2.exceptions.TemplateNotFound: index.html
```

**原因：**
Flask应用无法找到模板文件，通常是因为模板路径配置不正确。

**解决方案：**
确保Flask应用正确配置了模板和静态文件目录。在`app/__init__.py`中：

```python
def create_app():
    # 获取项目根目录
    import os
    basedir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
    
    # 创建Flask应用，指定模板和静态文件目录
    app = Flask(__name__, 
                template_folder=os.path.join(basedir, 'templates'),
                static_folder=os.path.join(basedir, 'static'))
```

**验证修复：**
```bash
# 测试首页访问
curl http://localhost:5000

# 运行测试
python -m pytest tests/test_app.py::AppTestCase::test_index_page -v
```

### 2. 配置错误

**问题描述：**
```
Configuration errors: OPENAI_API_KEY is required
```

**解决方案：**
1. 确保`.env`文件存在
2. 配置必要的API密钥：
```env
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_SEARCH_API_URL=http://************:18183/search
```

### 3. 依赖包问题

**问题描述：**
```
ImportError: cannot import name 'TimeoutError' from 'DrissionPage.errors'
```

**解决方案：**
已在代码中添加兼容性处理：
```python
try:
    from DrissionPage.errors import TimeoutError as DrissionTimeoutError
except ImportError:
    DrissionTimeoutError = TimeoutError
```

### 4. 端口占用

**问题描述：**
```
OSError: [Errno 98] Address already in use
```

**解决方案：**
```bash
# 查找占用端口的进程
netstat -tlnp | grep :5000

# 杀死进程
kill -9 <PID>

# 或者使用不同端口
python app.py --port 5001
```

### 5. 虚拟环境问题

**问题描述：**
依赖包找不到或版本冲突

**解决方案：**
```bash
# 重新创建虚拟环境
rm -rf venv
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 重新安装依赖
pip install -r requirements.txt
```

### 6. API调用失败

**问题描述：**
OpenAI API或Google搜索API调用失败

**解决方案：**
1. 检查网络连接
2. 验证API密钥
3. 检查API配额
4. 查看日志文件：
```bash
tail -f logs/app_*.log
```

### 7. 网页抓取失败

**问题描述：**
DrissionPage抓取网页失败

**解决方案：**
1. 确保Chrome浏览器已安装
2. 检查目标网站是否可访问
3. 调整超时设置：
```env
SCRAPING_TIMEOUT=60
MAX_RETRIES=5
```

## 调试技巧

### 1. 启用详细日志
```env
LOG_LEVEL=DEBUG
FLASK_DEBUG=True
```

### 2. 使用启动脚本
```bash
python start.py
```
启动脚本会自动检查环境和配置。

### 3. 运行测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_app.py -v
```

### 4. 检查服务状态
```bash
# 健康检查
curl http://localhost:5000/api/health

# 检查进程
ps aux | grep python
```

## 性能优化

### 1. 生产环境配置
```env
FLASK_ENV=production
FLASK_DEBUG=False
SCRAPING_TIMEOUT=60
MAX_RETRIES=5
```

### 2. 使用Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 3. 配置Nginx反向代理
参考`docs/DEPLOYMENT.md`中的Nginx配置。

## 联系支持

如果问题仍然存在，请：
1. 查看日志文件：`logs/app_*.log`
2. 运行诊断脚本：`python start.py`
3. 提供错误信息和环境详情
4. 查看项目文档：`docs/README.md`

## 更新日志

- **2025-07-02**: 修复TemplateNotFound错误
- **2025-07-02**: 更新Google搜索API地址
- **2025-07-02**: 添加DrissionPage兼容性处理
