"""
AI服务模块
集成第三方AI API，提供AI分析功能
"""
import requests
import time
import json
from typing import Dict, List, Optional
from flask import current_app
from app.utils.logger import get_logger

logger = get_logger(__name__)

class OpenAIService:
    """AI服务类 - 使用第三方服务商API"""

    def __init__(self):
        self.api_key = current_app.config['OPENAI_API_KEY']
        self.base_url = current_app.config['OPENAI_BASE_URL']
        self.model = current_app.config['OPENAI_MODEL']
        self.max_retries = current_app.config['MAX_RETRIES']
        self.request_delay = current_app.config['REQUEST_DELAY']
        self.timeout = current_app.config.get('OPENAI_TIMEOUT', 60)

        # 确保base_url以/结尾
        if not self.base_url.endswith('/'):
            self.base_url += '/'

        # 构建完整的API端点
        self.chat_endpoint = f"{self.base_url}chat/completions"
    
    def analyze_search_results(self, search_results: List[Dict], custom_prompt: str = "") -> str:
        """
        分析搜索结果
        
        Args:
            search_results: 搜索结果列表
            custom_prompt: 用户自定义提示词
            
        Returns:
            AI分析结果
        """
        logger.info(f"开始分析 {len(search_results)} 个搜索结果")
        
        # 构建分析提示词
        prompt = self._build_search_analysis_prompt(search_results, custom_prompt)
        
        try:
            response = self._call_openai_api(prompt)
            logger.info("搜索结果分析完成")
            return response
        except Exception as e:
            logger.error(f"搜索结果分析失败: {str(e)}")
            raise Exception(f"AI分析失败: {str(e)}")
    
    def analyze_webpage_content(self, content: str, url: str, custom_prompt: str = "") -> str:
        """
        分析网页内容
        
        Args:
            content: 网页内容
            url: 网页URL
            custom_prompt: 用户自定义提示词
            
        Returns:
            AI分析结果
        """
        logger.info(f"开始分析网页内容: {url}")
        
        # 构建内容分析提示词
        prompt = self._build_content_analysis_prompt(content, url, custom_prompt)
        
        try:
            response = self._call_openai_api(prompt)
            logger.info(f"网页内容分析完成: {url}")
            return response
        except Exception as e:
            logger.error(f"网页内容分析失败 {url}: {str(e)}")
            raise Exception(f"AI分析失败: {str(e)}")
    
    def _build_search_analysis_prompt(self, search_results: List[Dict], custom_prompt: str) -> str:
        """构建搜索结果分析提示词"""
        
        # 格式化搜索结果
        results_text = ""
        for i, result in enumerate(search_results, 1):
            results_text += f"{i}. 标题: {result.get('title', 'N/A')}\n"
            results_text += f"   URL: {result.get('url', 'N/A')}\n"
            results_text += f"   摘要: {result.get('snippet', 'N/A')}\n\n"
        
        # 基础提示词
        base_prompt = f"""
{results_text}
"""
        
        # 如果有自定义提示词，添加到末尾
        if custom_prompt.strip():
            base_prompt += f"\n\n用户特殊要求：\n{custom_prompt}"
        
        return base_prompt
    
    def _build_content_analysis_prompt(self, content: str, url: str, custom_prompt: str) -> str:
        """构建网页内容分析提示词"""
        
        # 截取内容长度以避免超出token限制
        max_content_length = 8000  # 保留足够的token给提示词和响应
        if len(content) > max_content_length:
            content = content[:max_content_length] + "...[内容已截断]"
        
        # 基础提示词
        base_prompt = f"""
请分析以下网页内容，并提供详细的分析报告：

网页URL: {url}

网页内容：
{content}

保持客观和专业。
"""
        
        # 如果有自定义提示词，添加到末尾
        if custom_prompt.strip():
            base_prompt += f"\n\n用户特殊要求：\n{custom_prompt}"
        
        return base_prompt
    
    def _call_openai_api(self, prompt: str) -> str:
        """
        调用第三方AI API

        Args:
            prompt: 提示词

        Returns:
            API响应内容
        """
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"调用AI API (尝试 {attempt + 1}/{self.max_retries})")

                # 构建请求头
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.api_key}',
                    'User-Agent': 'AI-Research-Tool/1.0'
                }

                # 构建请求体
                payload = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "你是一个专业的AI搜索调研助手，擅长分析和总结各种信息。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 2000,
                    "temperature": 0.7,
                    "stream": False
                }
                print(payload)

                logger.info(f"请求端点: {self.chat_endpoint}")
                logger.info(f"请求模型: {self.model}")

                # 发送HTTP请求
                response = requests.post(
                    self.chat_endpoint,
                    headers=headers,
                    json=payload,
                    timeout=self.timeout
                )

                # 检查HTTP状态码
                if response.status_code == 200:
                    response_data = response.json()

                    # 验证响应格式
                    if 'choices' in response_data and len(response_data['choices']) > 0:
                        content = response_data['choices'][0]['message']['content'].strip()
                        logger.debug("AI API调用成功")
                        print(content)
                        return content
                    else:
                        raise Exception("API响应格式错误：缺少choices字段")

                elif response.status_code == 429:
                    # 速率限制
                    logger.warning(f"AI API速率限制 (尝试 {attempt + 1}/{self.max_retries})")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.request_delay * (attempt + 1) * 2)
                        continue
                    else:
                        raise Exception("AI API速率限制，请稍后重试")

                elif response.status_code == 401:
                    raise Exception("AI API认证失败，请检查API密钥")

                elif response.status_code == 400:
                    error_msg = "请求参数错误"
                    try:
                        error_data = response.json()
                        if 'error' in error_data:
                            error_msg = error_data['error'].get('message', error_msg)
                    except:
                        pass
                    raise Exception(f"AI API请求错误: {error_msg}")

                else:
                    # 其他HTTP错误
                    error_msg = f"HTTP {response.status_code}"
                    try:
                        error_data = response.json()
                        if 'error' in error_data:
                            error_msg = error_data['error'].get('message', error_msg)
                    except:
                        error_msg = response.text[:200] if response.text else error_msg

                    logger.error(f"AI API错误 (尝试 {attempt + 1}/{self.max_retries}): {error_msg}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.request_delay * (attempt + 1))
                        continue
                    else:
                        raise Exception(f"AI API错误: {error_msg}")

            except requests.exceptions.Timeout:
                logger.warning(f"AI API请求超时 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception("AI API请求超时")

            except requests.exceptions.ConnectionError:
                logger.error(f"AI API连接失败 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception("AI API连接失败，请检查网络连接")

            except requests.exceptions.RequestException as e:
                logger.error(f"AI API请求异常 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception(f"AI API请求失败: {str(e)}")

            except json.JSONDecodeError:
                logger.error(f"AI API响应解析失败 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception("AI API响应格式错误")

            except Exception as e:
                logger.error(f"AI API调用失败 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception(f"AI API调用失败: {str(e)}")

        raise Exception("AI API调用失败，已达到最大重试次数")
