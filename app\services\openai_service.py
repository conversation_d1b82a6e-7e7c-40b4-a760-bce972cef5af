"""
OpenAI服务模块
集成OpenAI API，提供AI分析功能
"""
import openai
import time
from typing import Dict, List, Optional
from flask import current_app
from app.utils.logger import get_logger

logger = get_logger(__name__)

class OpenAIService:
    """OpenAI服务类"""
    
    def __init__(self):
        self.api_key = current_app.config['OPENAI_API_KEY']
        self.base_url = current_app.config['OPENAI_BASE_URL']
        self.model = current_app.config['OPENAI_MODEL']
        self.max_retries = current_app.config['MAX_RETRIES']
        self.request_delay = current_app.config['REQUEST_DELAY']
        
        # 初始化OpenAI客户端
        openai.api_key = self.api_key
        if self.base_url != 'https://api.openai.com/v1':
            openai.api_base = self.base_url
    
    def analyze_search_results(self, search_results: List[Dict], custom_prompt: str = "") -> str:
        """
        分析搜索结果
        
        Args:
            search_results: 搜索结果列表
            custom_prompt: 用户自定义提示词
            
        Returns:
            AI分析结果
        """
        logger.info(f"开始分析 {len(search_results)} 个搜索结果")
        
        # 构建分析提示词
        prompt = self._build_search_analysis_prompt(search_results, custom_prompt)
        
        try:
            response = self._call_openai_api(prompt)
            logger.info("搜索结果分析完成")
            return response
        except Exception as e:
            logger.error(f"搜索结果分析失败: {str(e)}")
            raise Exception(f"AI分析失败: {str(e)}")
    
    def analyze_webpage_content(self, content: str, url: str, custom_prompt: str = "") -> str:
        """
        分析网页内容
        
        Args:
            content: 网页内容
            url: 网页URL
            custom_prompt: 用户自定义提示词
            
        Returns:
            AI分析结果
        """
        logger.info(f"开始分析网页内容: {url}")
        
        # 构建内容分析提示词
        prompt = self._build_content_analysis_prompt(content, url, custom_prompt)
        
        try:
            response = self._call_openai_api(prompt)
            logger.info(f"网页内容分析完成: {url}")
            return response
        except Exception as e:
            logger.error(f"网页内容分析失败 {url}: {str(e)}")
            raise Exception(f"AI分析失败: {str(e)}")
    
    def _build_search_analysis_prompt(self, search_results: List[Dict], custom_prompt: str) -> str:
        """构建搜索结果分析提示词"""
        
        # 格式化搜索结果
        results_text = ""
        for i, result in enumerate(search_results, 1):
            results_text += f"{i}. 标题: {result.get('title', 'N/A')}\n"
            results_text += f"   URL: {result.get('url', 'N/A')}\n"
            results_text += f"   摘要: {result.get('snippet', 'N/A')}\n\n"
        
        # 基础提示词
        base_prompt = f"""
请分析以下搜索结果，并提供详细的分析报告：

搜索结果：
{results_text}

请从以下几个方面进行分析：
1. 结果概览：总结搜索结果的主要内容和类型
2. 相关性分析：评估结果与搜索关键词的相关性
3. 信息质量：评估信息来源的可靠性和权威性
4. 关键发现：提取最重要的信息和洞察
5. 建议行动：基于分析结果提供后续建议

请用中文回答，保持客观和专业。
"""
        
        # 如果有自定义提示词，添加到末尾
        if custom_prompt.strip():
            base_prompt += f"\n\n用户特殊要求：\n{custom_prompt}"
        
        return base_prompt
    
    def _build_content_analysis_prompt(self, content: str, url: str, custom_prompt: str) -> str:
        """构建网页内容分析提示词"""
        
        # 截取内容长度以避免超出token限制
        max_content_length = 8000  # 保留足够的token给提示词和响应
        if len(content) > max_content_length:
            content = content[:max_content_length] + "...[内容已截断]"
        
        # 基础提示词
        base_prompt = f"""
请分析以下网页内容，并提供详细的分析报告：

网页URL: {url}

网页内容：
{content}

请从以下几个方面进行分析：
1. 内容概要：总结网页的主要内容和主题
2. 关键信息：提取最重要的信息点
3. 内容类型：识别内容的性质（新闻、产品介绍、技术文档等）
4. 数据提取：如果包含结构化数据，请提取关键数据
5. 实用性评估：评估内容对用户需求的价值

请用中文回答，保持客观和专业。
"""
        
        # 如果有自定义提示词，添加到末尾
        if custom_prompt.strip():
            base_prompt += f"\n\n用户特殊要求：\n{custom_prompt}"
        
        return base_prompt
    
    def _call_openai_api(self, prompt: str) -> str:
        """
        调用OpenAI API
        
        Args:
            prompt: 提示词
            
        Returns:
            API响应内容
        """
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"调用OpenAI API (尝试 {attempt + 1}/{self.max_retries})")
                
                response = openai.ChatCompletion.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的AI搜索调研助手，擅长分析和总结各种信息。"},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=2000,
                    temperature=0.7,
                    timeout=30
                )
                
                content = response.choices[0].message.content.strip()
                logger.debug("OpenAI API调用成功")
                return content
                
            except openai.error.RateLimitError:
                logger.warning(f"OpenAI API速率限制 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1) * 2)  # 速率限制时等待更长时间
                    continue
                else:
                    raise Exception("OpenAI API速率限制，请稍后重试")
                    
            except openai.error.APIError as e:
                logger.error(f"OpenAI API错误 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception(f"OpenAI API错误: {str(e)}")
                    
            except openai.error.Timeout:
                logger.warning(f"OpenAI API超时 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception("OpenAI API请求超时")
                    
            except Exception as e:
                logger.error(f"OpenAI API调用失败 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                else:
                    raise Exception(f"OpenAI API调用失败: {str(e)}")
        
        raise Exception("OpenAI API调用失败，已达到最大重试次数")
