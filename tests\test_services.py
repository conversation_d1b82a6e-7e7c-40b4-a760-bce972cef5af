"""
服务层测试
"""
import unittest
from unittest.mock import Mock, patch
from app.services.search_service import SearchService
from app.services.openai_service import OpenAIService
from app.utils.error_handler import ValidationError

class SearchServiceTestCase(unittest.TestCase):
    """搜索服务测试用例"""
    
    def setUp(self):
        """测试前设置"""
        # 模拟Flask应用上下文
        self.mock_app = Mock()
        self.mock_app.config = {
            'GOOGLE_SEARCH_API_URL': 'http://test-api.com/search',
            'MAX_RETRIES': 3,
            'REQUEST_DELAY': 1,
            'SCRAPING_TIMEOUT': 30
        }
        
        with patch('app.services.search_service.current_app', self.mock_app):
            self.search_service = SearchService()
    
    def test_parse_search_results_with_results_key(self):
        """测试解析包含results键的搜索结果"""
        mock_data = {
            'results': [
                {
                    'title': '测试标题1',
                    'url': 'https://example1.com',
                    'snippet': '测试摘要1'
                },
                {
                    'title': '测试标题2',
                    'url': 'https://example2.com',
                    'snippet': '测试摘要2'
                }
            ]
        }
        
        results = self.search_service._parse_search_results(mock_data)
        
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]['title'], '测试标题1')
        self.assertEqual(results[0]['url'], 'https://example1.com')
        self.assertEqual(results[1]['title'], '测试标题2')
    
    def test_parse_search_results_with_items_key(self):
        """测试解析包含items键的搜索结果"""
        mock_data = {
            'items': [
                {
                    'title': '测试标题',
                    'link': 'https://example.com',
                    'snippet': '测试摘要'
                }
            ]
        }
        
        results = self.search_service._parse_search_results(mock_data)
        
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['title'], '测试标题')
        self.assertEqual(results[0]['url'], 'https://example.com')
    
    def test_validate_search_results(self):
        """测试搜索结果验证"""
        valid_results = [
            {
                'title': '有效标题',
                'url': 'https://example.com',
                'snippet': '有效摘要'
            }
        ]
        
        invalid_results = [
            {
                'title': '',  # 无标题
                'url': 'https://example.com',
                'snippet': '摘要'
            },
            {
                'title': '标题',
                'url': 'invalid-url',  # 无效URL
                'snippet': '摘要'
            }
        ]
        
        # 测试有效结果
        validated = self.search_service.validate_search_results(valid_results)
        self.assertEqual(len(validated), 1)
        
        # 测试无效结果
        validated = self.search_service.validate_search_results(invalid_results)
        self.assertEqual(len(validated), 0)

class OpenAIServiceTestCase(unittest.TestCase):
    """AI服务测试用例 (第三方API)"""

    def setUp(self):
        """测试前设置"""
        # 模拟Flask应用上下文
        self.mock_app = Mock()
        self.mock_app.config = {
            'OPENAI_API_KEY': 'test-key',
            'OPENAI_BASE_URL': 'https://api.newapi.pro/v1',
            'OPENAI_MODEL': 'gpt-3.5-turbo',
            'OPENAI_TIMEOUT': 60,
            'MAX_RETRIES': 3,
            'REQUEST_DELAY': 1
        }

        with patch('app.services.openai_service.current_app', self.mock_app):
            self.openai_service = OpenAIService()
    
    def test_build_search_analysis_prompt(self):
        """测试构建搜索分析提示词"""
        search_results = [
            {
                'title': '测试标题',
                'url': 'https://example.com',
                'snippet': '测试摘要'
            }
        ]
        
        prompt = self.openai_service._build_search_analysis_prompt(
            search_results, '重点关注技术信息'
        )
        
        self.assertIn('测试标题', prompt)
        self.assertIn('https://example.com', prompt)
        self.assertIn('重点关注技术信息', prompt)
    
    def test_build_content_analysis_prompt(self):
        """测试构建内容分析提示词"""
        content = '这是一个测试网页内容'
        url = 'https://example.com'
        custom_prompt = '提取关键信息'

        prompt = self.openai_service._build_content_analysis_prompt(
            content, url, custom_prompt
        )

        self.assertIn(content, prompt)
        self.assertIn(url, prompt)
        self.assertIn(custom_prompt, prompt)

    def test_call_openai_api_success(self):
        """测试AI API调用成功"""
        # 模拟成功的API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'choices': [
                {
                    'message': {
                        'content': '这是AI分析结果'
                    }
                }
            ]
        }

        # 配置mock
        with patch('app.services.openai_service.requests.post', return_value=mock_response):
            result = self.openai_service._call_openai_api('测试提示词')
            self.assertEqual(result, '这是AI分析结果')

    def test_call_openai_api_rate_limit(self):
        """测试AI API速率限制"""
        # 模拟速率限制响应
        mock_response = Mock()
        mock_response.status_code = 429

        with patch('app.services.openai_service.requests.post', return_value=mock_response):
            with self.assertRaises(Exception) as context:
                self.openai_service._call_openai_api('测试提示词')
            self.assertIn('速率限制', str(context.exception))

if __name__ == '__main__':
    unittest.main()
