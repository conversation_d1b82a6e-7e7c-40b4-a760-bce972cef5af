"""
错误处理工具模块
提供统一的错误处理和用户友好的错误消息
"""
import traceback
from typing import Dict, Any
from flask import jsonify, request
from app.utils.logger import get_logger

logger = get_logger(__name__)

class APIError(Exception):
    """API错误基类"""
    
    def __init__(self, message: str, status_code: int = 500, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or 'INTERNAL_ERROR'

class ValidationError(APIError):
    """验证错误"""
    
    def __init__(self, message: str):
        super().__init__(message, 400, 'VALIDATION_ERROR')

class SearchError(APIError):
    """搜索错误"""
    
    def __init__(self, message: str):
        super().__init__(message, 500, 'SEARCH_ERROR')

class OpenAIError(APIError):
    """OpenAI API错误"""
    
    def __init__(self, message: str):
        super().__init__(message, 500, 'OPENAI_ERROR')

class ScrapingError(APIError):
    """网页抓取错误"""
    
    def __init__(self, message: str):
        super().__init__(message, 500, 'SCRAPING_ERROR')

def handle_api_error(error: APIError) -> Dict[str, Any]:
    """
    处理API错误
    
    Args:
        error: API错误实例
        
    Returns:
        错误响应字典
    """
    logger.error(f"API错误 [{error.error_code}]: {error.message}")
    
    return {
        'error': error.message,
        'error_code': error.error_code,
        'status_code': error.status_code
    }

def handle_unexpected_error(error: Exception) -> Dict[str, Any]:
    """
    处理未预期的错误
    
    Args:
        error: 异常实例
        
    Returns:
        错误响应字典
    """
    error_id = generate_error_id()
    error_message = f"系统内部错误，错误ID: {error_id}"
    
    # 记录详细错误信息
    logger.error(f"未预期错误 [ID: {error_id}]: {str(error)}")
    logger.error(f"错误堆栈: {traceback.format_exc()}")
    logger.error(f"请求信息: {request.method} {request.url}")
    
    return {
        'error': error_message,
        'error_code': 'UNEXPECTED_ERROR',
        'error_id': error_id,
        'status_code': 500
    }

def generate_error_id() -> str:
    """生成错误ID"""
    import time
    import random
    import string
    
    timestamp = str(int(time.time()))
    random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
    return f"ERR-{timestamp}-{random_str}"

def validate_search_request(data: Dict[str, Any]) -> None:
    """
    验证搜索请求数据
    
    Args:
        data: 请求数据
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    if not data:
        raise ValidationError("请求数据不能为空")
    
    keywords = data.get('keywords', '').strip()
    if not keywords:
        raise ValidationError("搜索关键词不能为空")
    
    if len(keywords) > 200:
        raise ValidationError("搜索关键词长度不能超过200个字符")
    
    custom_prompt = data.get('custom_prompt', '')
    if custom_prompt and len(custom_prompt) > 1000:
        raise ValidationError("自定义提示词长度不能超过1000个字符")

def validate_deep_analysis_request(data: Dict[str, Any]) -> None:
    """
    验证深度分析请求数据
    
    Args:
        data: 请求数据
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    if not data:
        raise ValidationError("请求数据不能为空")
    
    urls = data.get('urls', [])
    if not urls:
        raise ValidationError("URL列表不能为空")
    
    if not isinstance(urls, list):
        raise ValidationError("URLs必须是列表格式")
    
    if len(urls) > 10:
        raise ValidationError("一次最多只能分析10个URL")
    
    for url in urls:
        if not isinstance(url, str) or not url.strip():
            raise ValidationError("URL不能为空")
        
        if not (url.startswith('http://') or url.startswith('https://')):
            raise ValidationError(f"无效的URL格式: {url}")
    
    custom_prompt = data.get('custom_prompt', '')
    if custom_prompt and len(custom_prompt) > 1000:
        raise ValidationError("自定义提示词长度不能超过1000个字符")

def create_error_response(error_data: Dict[str, Any]) -> tuple:
    """
    创建错误响应
    
    Args:
        error_data: 错误数据
        
    Returns:
        Flask响应元组
    """
    status_code = error_data.get('status_code', 500)
    response_data = {
        'error': error_data.get('error', '未知错误'),
        'error_code': error_data.get('error_code', 'UNKNOWN_ERROR')
    }
    
    # 添加错误ID（如果有）
    if 'error_id' in error_data:
        response_data['error_id'] = error_data['error_id']
    
    return jsonify(response_data), status_code

def log_request_info():
    """记录请求信息"""
    logger.info(f"请求: {request.method} {request.url}")
    logger.debug(f"请求头: {dict(request.headers)}")
    
    if request.is_json:
        logger.debug(f"请求数据: {request.get_json()}")

def get_user_friendly_error_message(error_code: str) -> str:
    """
    获取用户友好的错误消息
    
    Args:
        error_code: 错误代码
        
    Returns:
        用户友好的错误消息
    """
    error_messages = {
        'VALIDATION_ERROR': '输入数据有误，请检查后重试',
        'SEARCH_ERROR': '搜索服务暂时不可用，请稍后重试',
        'OPENAI_ERROR': 'AI分析服务暂时不可用，请稍后重试',
        'SCRAPING_ERROR': '网页抓取失败，可能是网络问题或目标网站限制访问',
        'TIMEOUT_ERROR': '请求超时，请稍后重试',
        'RATE_LIMIT_ERROR': '请求过于频繁，请稍后重试',
        'NETWORK_ERROR': '网络连接失败，请检查网络后重试',
        'UNEXPECTED_ERROR': '系统内部错误，我们已记录此问题并会尽快修复',
        'CONFIGURATION_ERROR': '系统配置错误，请联系管理员'
    }
    
    return error_messages.get(error_code, '未知错误，请稍后重试')
