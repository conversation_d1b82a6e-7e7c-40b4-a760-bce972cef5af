#!/usr/bin/env python
"""
AI搜索调研助手启动脚本
提供便捷的启动方式和环境检查
"""
import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查虚拟环境
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  建议在虚拟环境中运行")
    else:
        print("✅ 虚拟环境已激活")
    
    # 检查必要文件
    required_files = [
        'app.py',
        'config.py',
        'requirements.txt',
        '.env'
    ]
    
    for file in required_files:
        if not Path(file).exists():
            if file == '.env':
                print(f"⚠️  {file} 不存在，请复制 .env.example 并配置")
            else:
                print(f"❌ 缺少必要文件: {file}")
                return False
        else:
            print(f"✅ {file} 存在")
    
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    try:
        import flask
        print(f"✅ Flask: {flask.__version__}")
    except ImportError:
        print("❌ Flask未安装")
        return False
    
    try:
        import requests
        print(f"✅ Requests: {requests.__version__}")
    except ImportError:
        print("❌ Requests未安装")
        return False
    
    try:
        import DrissionPage
        print(f"✅ DrissionPage: {DrissionPage.__version__}")
    except ImportError:
        print("❌ DrissionPage未安装")
        return False
    
    return True

def check_config():
    """检查配置"""
    print("\n⚙️  检查配置...")
    
    if not Path('.env').exists():
        print("❌ .env文件不存在")
        return False
    
    # 读取.env文件
    env_vars = {}
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    # 检查必要配置
    required_configs = [
        'OPENAI_API_KEY',
        'GOOGLE_SEARCH_API_URL'
    ]
    
    for config in required_configs:
        if config not in env_vars or not env_vars[config] or env_vars[config] == 'your_openai_api_key_here':
            print(f"⚠️  {config} 未正确配置")
        else:
            print(f"✅ {config} 已配置")
    
    return True

def create_directories():
    """创建必要目录"""
    print("\n📁 创建必要目录...")
    
    directories = ['logs', 'static/uploads']
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory} 目录已创建")

def start_application():
    """启动应用"""
    print("\n🚀 启动AI搜索调研助手...")
    print("=" * 50)
    print("应用将在以下地址启动:")
    print("🌐 http://localhost:5000")
    print("🌐 http://127.0.0.1:5000")
    print("=" * 50)
    print("按 Ctrl+C 停止应用")
    print("=" * 50)
    
    try:
        # 启动Flask应用
        os.system('python app.py')
    except KeyboardInterrupt:
        print("\n👋 应用已停止")

def main():
    """主函数"""
    print("🤖 AI搜索调研助手启动器")
    print("=" * 50)
    
    # 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请修复后重试")
        return
    
    # 依赖检查
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请运行: pip install -r requirements.txt")
        return
    
    # 配置检查
    check_config()
    
    # 创建目录
    create_directories()
    
    # 启动应用
    start_application()

if __name__ == '__main__':
    main()
