# AI搜索调研助手

一个基于Python + Bootstrap的智能搜索调研Web应用，集成Google搜索API和OpenAI API，提供智能搜索分析和深度网页内容分析功能。

## 功能特性

### 核心功能
- **智能搜索**：集成内部Google搜索API，支持关键词搜索
- **AI分析**：使用OpenAI API对搜索结果进行智能分析和总结
- **深度分析**：可选的网页内容深度抓取和二次AI分析
- **自定义提示词**：支持用户自定义AI分析方向

### 主要应用场景
1. **特定特征页面搜索**：如医药公司的药品pipeline页面搜索和分析
2. **关键词匹配页面搜索**：如投资者关系页面搜索，自动生成数据采集规则
3. **竞品调研**：批量分析竞争对手网站内容
4. **市场研究**：收集和分析行业相关信息

## 技术架构

### 后端技术栈
- **框架**：Flask 3.0.0
- **网页抓取**：DrissionPage *******
- **AI服务**：OpenAI API
- **HTTP请求**：requests
- **配置管理**：python-dotenv

### 前端技术栈
- **UI框架**：Bootstrap 5.3.0
- **图标**：Font Awesome 6.0.0
- **JavaScript**：原生JS + jQuery 3.6.0

## 快速开始

### 环境要求
- Python 3.8+
- Windows/Linux/macOS
- 网络连接（用于API调用）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ai_research_tool
```

2. **创建虚拟环境**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
# 复制配置文件模板
cp .env.example .env

# 编辑.env文件，填入必要的API密钥
```

5. **启动应用**
```bash
python app.py
```

6. **访问应用**
打开浏览器访问：http://localhost:5000

## 配置说明

### 必需配置
在`.env`文件中配置以下必需参数：

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Google搜索API配置
GOOGLE_SEARCH_API_URL=http://************:18183/search
```

### 可选配置
```env
# Flask应用配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here

# 网页抓取配置
SCRAPING_TIMEOUT=30
MAX_RETRIES=3
REQUEST_DELAY=1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

## 使用指南

### 基础搜索
1. 在首页输入搜索关键词
2. 可选择添加自定义分析提示词
3. 点击"开始搜索分析"
4. 查看搜索结果和AI分析报告

### 深度分析
1. 在搜索表单中勾选"启用深度分析"
2. 完成基础搜索后，选择要深度分析的URL
3. 点击"开始深度分析"
4. 等待网页抓取和AI分析完成
5. 查看详细的深度分析报告

### 自定义提示词示例
- **医药研发分析**：`重点关注药品研发管线信息，提取公司名称、药品名称、研发阶段等关键信息`
- **投资者关系分析**：`识别投资者关系页面，提取财务数据、新闻发布、投资者联系方式等信息`
- **技术文档分析**：`分析技术文档内容，提取API接口、技术规格、使用方法等技术信息`

## API接口

### 搜索接口
```
POST /api/search
Content-Type: application/json

{
    "keywords": "搜索关键词",
    "custom_prompt": "自定义分析提示词（可选）"
}
```

### 深度分析接口
```
POST /api/deep-analysis
Content-Type: application/json

{
    "urls": ["https://example1.com", "https://example2.com"],
    "custom_prompt": "自定义分析提示词（可选）"
}
```

### 健康检查接口
```
GET /api/health
```

## 项目结构

```
ai_research_tool/
├── app/                    # 应用程序主目录
│   ├── __init__.py        # 应用工厂
│   ├── routes.py          # 路由定义
│   ├── services/          # 服务层
│   │   ├── search_service.py      # 搜索服务
│   │   ├── openai_service.py      # OpenAI服务
│   │   └── scraping_service.py    # 网页抓取服务
│   └── utils/             # 工具模块
│       ├── logger.py      # 日志工具
│       └── error_handler.py       # 错误处理
├── static/                # 静态文件
│   ├── css/              # 样式文件
│   └── js/               # JavaScript文件
├── templates/             # HTML模板
├── tests/                 # 测试文件
├── logs/                  # 日志文件
├── docs/                  # 文档目录
├── config.py             # 配置管理
├── app.py                # 应用入口
├── requirements.txt      # 依赖列表
└── .env.example         # 配置模板
```

## 故障排除

### 常见问题

1. **OpenAI API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 检查API配额是否充足

2. **Google搜索API无响应**
   - 确认API URL是否正确
   - 检查网络连接
   - 查看日志文件获取详细错误信息

3. **网页抓取失败**
   - 目标网站可能有反爬虫机制
   - 网络连接问题
   - 页面加载超时

4. **页面加载缓慢**
   - 检查网络连接速度
   - 减少同时分析的URL数量
   - 调整超时设置

### 日志查看
应用日志保存在`logs/`目录下，可以通过查看日志文件获取详细的错误信息：

```bash
# 查看最新日志
tail -f logs/app_20250702.log

# 搜索错误信息
grep "ERROR" logs/app_20250702.log
```

## 开发指南

### 运行测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试文件
python -m pytest tests/test_app.py

# 运行测试并显示覆盖率
python -m pytest tests/ --cov=app
```

### 代码规范
- 遵循PEP 8代码规范
- 使用类型提示
- 编写详细的文档字符串
- 添加适当的错误处理

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 技术支持
