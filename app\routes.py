"""
应用程序路由模块
定义所有的Web路由和API端点
"""
from flask import Blueprint, render_template, request, jsonify, current_app
from app.services.search_service import SearchService
from app.services.openai_service import OpenAIService
from app.services.scraping_service import ScrapingService
from app.utils.logger import get_logger
from app.utils.error_handler import (
    APIError, ValidationError, SearchError, OpenAIError, ScrapingError,
    handle_api_error, handle_unexpected_error, create_error_response,
    validate_search_request, validate_deep_analysis_request, log_request_info
)

# 创建蓝图
main_bp = Blueprint('main', __name__)
logger = get_logger(__name__)

# 错误处理装饰器
def handle_errors(f):
    """错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            log_request_info()
            return f(*args, **kwargs)
        except APIError as e:
            error_data = handle_api_error(e)
            return create_error_response(error_data)
        except Exception as e:
            error_data = handle_unexpected_error(e)
            return create_error_response(error_data)
    wrapper.__name__ = f.__name__
    return wrapper

@main_bp.route('/')
def index():
    """主页"""
    return render_template('index.html')

@main_bp.route('/api/search', methods=['POST'])
@handle_errors
def search():
    """搜索API端点"""
    data = request.get_json()

    # 验证请求数据
    validate_search_request(data)

    keywords = data.get('keywords', '').strip()
    custom_prompt = data.get('custom_prompt', '').strip()

    logger.info(f"开始搜索: {keywords}")

    try:
        # 执行搜索
        search_service = SearchService()
        search_results = search_service.search(keywords)

        if not search_results:
            raise SearchError('搜索未返回结果，请尝试其他关键词')

        # 验证搜索结果
        search_results = search_service.validate_search_results(search_results)

        if not search_results:
            raise SearchError('搜索结果验证失败，未找到有效结果')

    except Exception as e:
        logger.error(f"搜索服务错误: {str(e)}")
        raise SearchError(f'搜索失败: {str(e)}')

    try:
        # 使用OpenAI分析搜索结果
        openai_service = OpenAIService()
        analysis = openai_service.analyze_search_results(search_results, custom_prompt)

    except Exception as e:
        logger.error(f"OpenAI分析错误: {str(e)}")
        raise OpenAIError(f'AI分析失败: {str(e)}')

    response_data = {
        'keywords': keywords,
        'search_results': search_results,
        'analysis': analysis,
        'total_results': len(search_results)
    }

    logger.info(f"搜索完成: {keywords}, 结果数量: {len(search_results)}")
    return jsonify(response_data)

@main_bp.route('/api/deep-analysis', methods=['POST'])
@handle_errors
def deep_analysis():
    """深度分析API端点"""
    data = request.get_json()

    # 验证请求数据
    validate_deep_analysis_request(data)

    urls = data.get('urls', [])
    custom_prompt = data.get('custom_prompt', '').strip()

    logger.info(f"开始深度分析: {len(urls)} 个URL")

    # 网页内容抓取
    scraping_service = ScrapingService()
    scraped_data = []

    for url in urls:
        try:
            content = scraping_service.scrape_url(url)
            if content:
                scraped_data.append({
                    'url': url,
                    'content': content,
                    'status': 'success'
                })
            else:
                scraped_data.append({
                    'url': url,
                    'content': '',
                    'status': 'failed',
                    'error': '无法获取页面内容'
                })
        except Exception as e:
            logger.error(f"抓取URL失败 {url}: {str(e)}")
            scraped_data.append({
                'url': url,
                'content': '',
                'status': 'failed',
                'error': str(e)
            })

    # 使用OpenAI分析抓取的内容
    openai_service = OpenAIService()
    analysis_results = []

    for data_item in scraped_data:
        if data_item['status'] == 'success' and data_item['content']:
            try:
                analysis = openai_service.analyze_webpage_content(
                    data_item['content'],
                    data_item['url'],
                    custom_prompt
                )
                analysis_results.append({
                    'url': data_item['url'],
                    'analysis': analysis,
                    'status': 'success'
                })
            except Exception as e:
                logger.error(f"分析内容失败 {data_item['url']}: {str(e)}")
                analysis_results.append({
                    'url': data_item['url'],
                    'analysis': '',
                    'status': 'failed',
                    'error': str(e)
                })
        else:
            analysis_results.append({
                'url': data_item['url'],
                'analysis': '',
                'status': 'failed',
                'error': data_item.get('error', '内容抓取失败')
            })

    # 清理资源
    try:
        scraping_service.close()
    except:
        pass

    response_data = {
        'scraped_data': scraped_data,
        'analysis_results': analysis_results,
        'total_analyzed': len([r for r in analysis_results if r['status'] == 'success'])
    }

    logger.info(f"深度分析完成: {len(analysis_results)} 个结果")
    return jsonify(response_data)

@main_bp.route('/api/health')
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'message': 'AI搜索调研助手运行正常'
    })
