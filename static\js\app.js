/**
 * AI搜索调研助手前端JavaScript
 */

// 全局变量
let currentSearchResults = [];
let isSearching = false;
let isDeepAnalyzing = false;

/**
 * 初始化搜索表单
 */
function initializeSearchForm() {
    const searchForm = document.getElementById('search-form');
    const searchBtn = document.getElementById('search-btn');
    const deepAnalysisCheckbox = document.getElementById('enable-deep-analysis');
    
    // 搜索表单提交事件
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        if (!isSearching) {
            performSearch();
        }
    });
    
    // 深度分析按钮事件
    const deepAnalysisBtn = document.getElementById('start-deep-analysis-btn');
    if (deepAnalysisBtn) {
        deepAnalysisBtn.addEventListener('click', function() {
            if (!isDeepAnalyzing) {
                performDeepAnalysis();
            }
        });
    }
}

/**
 * 执行搜索
 */
function performSearch() {
    const keywords = document.getElementById('keywords').value.trim();
    const customPrompt = document.getElementById('custom-prompt').value.trim();
    const enableDeepAnalysis = document.getElementById('enable-deep-analysis').checked;
    
    if (!keywords) {
        showMessage('请输入搜索关键词', 'warning');
        return;
    }
    
    // 显示加载状态
    showLoading('正在搜索中，请稍候...');
    hideResults();
    setSearching(true);
    
    // 发送搜索请求
    fetch('/api/search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            keywords: keywords,
            custom_prompt: customPrompt
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        setSearching(false);
        
        if (data.error) {
            showMessage(data.error, 'danger');
            return;
        }
        
        // 保存搜索结果
        currentSearchResults = data.search_results || [];
        
        // 显示搜索结果
        displaySearchResults(data);
        
        // 如果启用了深度分析，显示URL选择界面
        if (enableDeepAnalysis && currentSearchResults.length > 0) {
            showDeepAnalysisSection();
        }
        
        showMessage(`搜索完成！找到 ${currentSearchResults.length} 个结果`, 'success');
    })
    .catch(error => {
        hideLoading();
        setSearching(false);
        console.error('搜索错误:', error);
        showMessage('搜索失败，请稍后重试', 'danger');
    });
}

/**
 * 显示搜索结果
 */
function displaySearchResults(data) {
    const resultsArea = document.getElementById('results-area');
    const resultsCount = document.getElementById('results-count');
    const resultsList = document.getElementById('search-results-list');
    const aiAnalysis = document.getElementById('ai-analysis');
    
    // 更新结果数量
    resultsCount.textContent = data.total_results || 0;
    
    // 显示搜索结果列表
    resultsList.innerHTML = '';
    if (data.search_results && data.search_results.length > 0) {
        data.search_results.forEach((result, index) => {
            const resultItem = createSearchResultItem(result, index);
            resultsList.appendChild(resultItem);
        });
    } else {
        resultsList.innerHTML = '<p class="text-muted">未找到搜索结果</p>';
    }
    
    // 显示AI分析结果
    if (data.analysis) {
        aiAnalysis.innerHTML = formatAnalysisContent(data.analysis);
    } else {
        aiAnalysis.innerHTML = '<p class="text-muted">暂无AI分析结果</p>';
    }
    
    // 显示结果区域
    resultsArea.style.display = 'block';
    resultsArea.classList.add('fade-in');
}

/**
 * 创建搜索结果项
 */
function createSearchResultItem(result, index) {
    const div = document.createElement('div');
    div.className = 'search-result-item';

    div.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
                <h6 class="search-result-title">
                    <a href="${result.url}" target="_blank">${result.title || '无标题'}</a>
                </h6>
                <div class="search-result-url">${result.display_url || result.url}</div>
                <div class="search-result-snippet">${result.snippet || '无摘要'}</div>
            </div>
            <div class="ms-3">
                <span class="badge bg-secondary">#${index + 1}</span>
            </div>
        </div>
    `;

    return div;
}

/**
 * 显示深度分析选项
 */
function showDeepAnalysisSection() {
    const deepAnalysisSection = document.getElementById('deep-analysis-section');
    const urlSelectionList = document.getElementById('url-selection-list');

    // 清空现有内容
    urlSelectionList.innerHTML = '';

    // 创建URL选择项
    currentSearchResults.forEach((result, index) => {
        const selectionItem = createUrlSelectionItem(result, index);
        urlSelectionList.appendChild(selectionItem);
    });

    // 显示深度分析区域
    deepAnalysisSection.style.display = 'block';
    deepAnalysisSection.classList.add('fade-in');
}

/**
 * 创建URL选择项
 */
function createUrlSelectionItem(result, index) {
    const div = document.createElement('div');
    div.className = 'url-selection-item';

    div.innerHTML = `
        <div class="form-check">
            <input class="form-check-input" type="checkbox" value="${result.url}"
                   id="url-${index}" checked>
            <label class="form-check-label" for="url-${index}">
                <div class="fw-bold">${result.title || '无标题'}</div>
                <div class="text-muted small">${result.display_url || result.url}</div>
            </label>
        </div>
    `;

    return div;
}

/**
 * 执行深度分析
 */
function performDeepAnalysis() {
    const selectedUrls = getSelectedUrls();
    const customPrompt = document.getElementById('custom-prompt').value.trim();

    if (selectedUrls.length === 0) {
        showMessage('请至少选择一个URL进行深度分析', 'warning');
        return;
    }

    // 显示加载状态
    showLoading(`正在深度分析 ${selectedUrls.length} 个网页，请耐心等待...`);
    setDeepAnalyzing(true);

    // 发送深度分析请求
    fetch('/api/deep-analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            urls: selectedUrls,
            custom_prompt: customPrompt
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        setDeepAnalyzing(false);

        if (data.error) {
            showMessage(data.error, 'danger');
            return;
        }

        // 显示深度分析结果
        displayDeepAnalysisResults(data);

        showMessage(`深度分析完成！成功分析 ${data.total_analyzed} 个网页`, 'success');
    })
    .catch(error => {
        hideLoading();
        setDeepAnalyzing(false);
        console.error('深度分析错误:', error);
        showMessage('深度分析失败，请稍后重试', 'danger');
    });
}

/**
 * 获取选中的URL列表
 */
function getSelectedUrls() {
    const checkboxes = document.querySelectorAll('#url-selection-list input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(checkbox => checkbox.value);
}

/**
 * 显示深度分析结果
 */
function displayDeepAnalysisResults(data) {
    const deepAnalysisResults = document.getElementById('deep-analysis-results');
    const deepAnalysisContent = document.getElementById('deep-analysis-content');

    // 清空现有内容
    deepAnalysisContent.innerHTML = '';

    if (data.analysis_results && data.analysis_results.length > 0) {
        data.analysis_results.forEach((result, index) => {
            const analysisItem = createDeepAnalysisItem(result, index);
            deepAnalysisContent.appendChild(analysisItem);
        });
    } else {
        deepAnalysisContent.innerHTML = '<p class="text-muted">暂无深度分析结果</p>';
    }

    // 显示深度分析结果区域
    deepAnalysisResults.style.display = 'block';
    deepAnalysisResults.classList.add('fade-in');
}

/**
 * 创建深度分析结果项
 */
function createDeepAnalysisItem(result, index) {
    const div = document.createElement('div');
    div.className = `deep-analysis-item ${result.status}`;

    const statusIcon = result.status === 'success' ?
        '<i class="fas fa-check-circle text-success"></i>' :
        '<i class="fas fa-exclamation-circle text-danger"></i>';

    div.innerHTML = `
        <div class="d-flex align-items-start">
            <div class="me-3 mt-1">${statusIcon}</div>
            <div class="flex-grow-1">
                <div class="deep-analysis-url">
                    <span class="status-indicator status-${result.status}"></span>
                    ${result.url}
                </div>
                ${result.status === 'success' ?
                    `<div class="deep-analysis-content">${formatAnalysisContent(result.analysis)}</div>` :
                    `<div class="text-danger">分析失败: ${result.error || '未知错误'}</div>`
                }
            </div>
        </div>
    `;

    return div;
}

/**
 * 格式化分析内容
 */
function formatAnalysisContent(content) {
    if (!content) return '';

    // 简单的文本格式化
    return content
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>');
}

/**
 * 显示消息提示
 */
function showMessage(message, type = 'info') {
    const messageArea = document.getElementById('message-area');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    messageArea.appendChild(alertDiv);

    // 自动隐藏消息
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * 显示加载状态
 */
function showLoading(text = '加载中...') {
    const loadingArea = document.getElementById('loading-area');
    const loadingText = document.getElementById('loading-text');

    loadingText.textContent = text;
    loadingArea.style.display = 'block';
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loadingArea = document.getElementById('loading-area');
    loadingArea.style.display = 'none';
}

/**
 * 隐藏结果区域
 */
function hideResults() {
    const resultsArea = document.getElementById('results-area');
    const deepAnalysisSection = document.getElementById('deep-analysis-section');
    const deepAnalysisResults = document.getElementById('deep-analysis-results');

    resultsArea.style.display = 'none';
    deepAnalysisSection.style.display = 'none';
    deepAnalysisResults.style.display = 'none';
}

/**
 * 设置搜索状态
 */
function setSearching(searching) {
    isSearching = searching;
    const searchBtn = document.getElementById('search-btn');
    const searchForm = document.getElementById('search-form');

    if (searching) {
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>搜索中...';
        searchForm.classList.add('disabled');
    } else {
        searchBtn.disabled = false;
        searchBtn.innerHTML = '<i class="fas fa-search me-2"></i>开始搜索分析';
        searchForm.classList.remove('disabled');
    }
}

/**
 * 设置深度分析状态
 */
function setDeepAnalyzing(analyzing) {
    isDeepAnalyzing = analyzing;
    const deepAnalysisBtn = document.getElementById('start-deep-analysis-btn');

    if (deepAnalysisBtn) {
        if (analyzing) {
            deepAnalysisBtn.disabled = true;
            deepAnalysisBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>分析中...';
        } else {
            deepAnalysisBtn.disabled = false;
            deepAnalysisBtn.innerHTML = '<i class="fas fa-microscope me-2"></i>开始深度分析';
        }
    }
}
