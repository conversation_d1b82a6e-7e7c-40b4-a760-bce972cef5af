# -*- coding:utf-8 -*-
"""
<AUTHOR> g1879
@Contact  : <EMAIL>
@Copyright: (c) 2024 by g1879, Inc. All Rights Reserved.
@License  : BSD 3-Clause.
"""
from configparser import RawConfigParser
from pathlib import Path
from typing import Any, Optional, Union


class OptionsManager(object):
    ini_path: Optional[Path] = ...
    file_exists: bool = ...
    _conf: RawConfigParser = ...

    def __init__(self, path: Union[Path, str] = None): ...

    def __getattr__(self, item) -> dict: ...

    def get_value(self, section: str, item: str) -> Any: ...

    def get_option(self, section: str) -> dict: ...

    def set_item(self, section: str, item: str, value: Any) -> None: ...

    def remove_item(self, section: str, item: str) -> None: ...

    def save(self, path: str = None) -> str: ...

    def save_to_default(self) -> str: ...

    def show(self) -> None: ...
