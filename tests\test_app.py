"""
应用程序基础测试
"""
import unittest
import json
from app import create_app
from config import TestingConfig

class AppTestCase(unittest.TestCase):
    """应用程序测试用例"""
    
    def setUp(self):
        """测试前设置"""
        self.app = create_app()
        self.app.config.from_object(TestingConfig)
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """测试后清理"""
        self.app_context.pop()
    
    def test_index_page(self):
        """测试首页"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'AI\xe6\x90\x9c\xe7\xb4\xa2\xe8\xb0\x83\xe7\xa0\x94\xe5\x8a\xa9\xe6\x89\x8b', response.data)  # AI搜索调研助手
    
    def test_health_check(self):
        """测试健康检查"""
        response = self.client.get('/api/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
    
    def test_search_api_validation(self):
        """测试搜索API验证"""
        # 测试空请求
        response = self.client.post('/api/search', 
                                  data=json.dumps({}),
                                  content_type='application/json')
        self.assertEqual(response.status_code, 400)
        
        # 测试空关键词
        response = self.client.post('/api/search',
                                  data=json.dumps({'keywords': ''}),
                                  content_type='application/json')
        self.assertEqual(response.status_code, 400)
        
        # 测试关键词过长
        long_keywords = 'a' * 201
        response = self.client.post('/api/search',
                                  data=json.dumps({'keywords': long_keywords}),
                                  content_type='application/json')
        self.assertEqual(response.status_code, 400)
    
    def test_deep_analysis_api_validation(self):
        """测试深度分析API验证"""
        # 测试空URL列表
        response = self.client.post('/api/deep-analysis',
                                  data=json.dumps({'urls': []}),
                                  content_type='application/json')
        self.assertEqual(response.status_code, 400)
        
        # 测试无效URL格式
        response = self.client.post('/api/deep-analysis',
                                  data=json.dumps({'urls': ['invalid-url']}),
                                  content_type='application/json')
        self.assertEqual(response.status_code, 400)
        
        # 测试URL数量过多
        too_many_urls = ['https://example.com'] * 11
        response = self.client.post('/api/deep-analysis',
                                  data=json.dumps({'urls': too_many_urls}),
                                  content_type='application/json')
        self.assertEqual(response.status_code, 400)

if __name__ == '__main__':
    unittest.main()
