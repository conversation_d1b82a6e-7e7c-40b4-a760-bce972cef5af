"""
AI搜索调研助手应用程序初始化模块
"""
import os
import logging
from flask import Flask
from config import get_config

def create_app():
    """应用程序工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    config_class = get_config()
    app.config.from_object(config_class)
    
    # 验证配置
    try:
        config_class.validate_config()
    except ValueError as e:
        app.logger.error(f"Configuration validation failed: {e}")
        raise
    
    # 设置日志
    setup_logging(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 创建必要的目录
    create_directories()
    
    return app

def setup_logging(app):
    """设置日志配置"""
    if not app.debug:
        # 确保日志目录存在
        log_dir = os.path.dirname(app.config['LOG_FILE'])
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置文件日志处理器
        file_handler = logging.FileHandler(app.config['LOG_FILE'])
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(getattr(logging, app.config['LOG_LEVEL']))
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(getattr(logging, app.config['LOG_LEVEL']))
        app.logger.info('AI Research Tool startup')

def register_blueprints(app):
    """注册蓝图"""
    from app.routes import main_bp
    app.register_blueprint(main_bp)

def create_directories():
    """创建必要的目录"""
    directories = ['logs', 'static/uploads']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
