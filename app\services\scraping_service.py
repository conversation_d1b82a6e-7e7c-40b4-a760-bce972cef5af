"""
网页抓取服务模块
使用DrissionPage实现网页内容抓取
"""
import time
import re
from typing import Optional, Dict
from DrissionPage import ChromiumPage, ChromiumOptions
from DrissionPage.errors import PageDisconnectedError, ElementNotFoundError
try:
    from DrissionPage.errors import TimeoutError as DrissionTimeoutError
except ImportError:
    # 如果DrissionPage没有TimeoutError，使用内置的TimeoutError
    DrissionTimeoutError = TimeoutError
from flask import current_app
from app.utils.logger import get_logger

logger = get_logger(__name__)

class ScrapingService:
    """网页抓取服务类"""
    
    def __init__(self):
        self.timeout = current_app.config['SCRAPING_TIMEOUT']
        self.max_retries = current_app.config['MAX_RETRIES']
        self.request_delay = current_app.config['REQUEST_DELAY']
        self.page = None
    
    def scrape_url(self, url: str) -> Optional[str]:
        """
        抓取指定URL的网页内容
        
        Args:
            url: 要抓取的URL
            
        Returns:
            网页文本内容，失败时返回None
        """
        logger.info(f"开始抓取网页: {url}")
        
        for attempt in range(self.max_retries):
            try:
                # 初始化浏览器页面
                if not self.page:
                    self.page = self._create_page()
                
                # 访问网页
                logger.debug(f"访问URL (尝试 {attempt + 1}/{self.max_retries}): {url}")
                self.page.get(url, timeout=self.timeout)
                
                # 等待页面加载
                time.sleep(2)
                
                # 检查页面是否加载成功
                if not self._is_page_loaded():
                    logger.warning(f"页面加载不完整: {url}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.request_delay)
                        continue
                
                # 提取页面内容
                content = self._extract_content()
                
                if content and len(content.strip()) > 100:  # 确保获取到有意义的内容
                    logger.info(f"网页抓取成功: {url}, 内容长度: {len(content)}")
                    return content
                else:
                    logger.warning(f"获取的内容过短或为空: {url}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.request_delay)
                        continue
                
            except (DrissionTimeoutError, TimeoutError):
                logger.warning(f"页面加载超时 (尝试 {attempt + 1}/{self.max_retries}): {url}")
                if attempt < self.max_retries - 1:
                    self._reset_page()
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
                    
            except PageDisconnectedError:
                logger.warning(f"页面连接断开 (尝试 {attempt + 1}/{self.max_retries}): {url}")
                self._reset_page()
                if attempt < self.max_retries - 1:
                    time.sleep(self.request_delay)
                    continue
                    
            except Exception as e:
                logger.error(f"抓取网页时发生错误 (尝试 {attempt + 1}/{self.max_retries}): {url}, 错误: {str(e)}")
                if attempt < self.max_retries - 1:
                    self._reset_page()
                    time.sleep(self.request_delay * (attempt + 1))
                    continue
        
        logger.error(f"网页抓取失败，已达到最大重试次数: {url}")
        return None
    
    def _create_page(self) -> ChromiumPage:
        """创建浏览器页面实例"""
        try:
            # 配置浏览器选项
            options = ChromiumOptions()
            options.headless(True)  # 无头模式
            options.no_imgs(True)   # 不加载图片
            options.no_js(False)    # 允许JavaScript（某些页面需要）
            options.set_argument('--disable-gpu')
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-extensions')
            options.set_argument('--disable-plugins')
            options.set_argument('--disable-images')
            options.set_argument('--disable-javascript')  # 禁用JS以提高速度
            options.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            # 创建页面实例
            page = ChromiumPage(addr_or_opts=options)
            page.set.timeouts(base=self.timeout, page_load=self.timeout)
            
            logger.debug("浏览器页面创建成功")
            return page
            
        except Exception as e:
            logger.error(f"创建浏览器页面失败: {str(e)}")
            raise Exception(f"无法初始化浏览器: {str(e)}")
    
    def _is_page_loaded(self) -> bool:
        """检查页面是否加载完成"""
        try:
            # 检查页面标题
            title = self.page.title
            if not title or title.strip() == "":
                return False
            
            # 检查页面内容
            body = self.page('tag:body')
            if not body:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _extract_content(self) -> str:
        """提取页面文本内容"""
        try:
            # 移除不需要的元素
            self._remove_unwanted_elements()
            
            # 获取主要内容
            content_selectors = [
                'main',
                'article', 
                '.content',
                '.main-content',
                '.post-content',
                '.entry-content',
                '#content',
                '#main',
                'body'
            ]
            
            content = ""
            for selector in content_selectors:
                try:
                    element = self.page(selector)
                    if element:
                        text = element.text
                        if text and len(text.strip()) > len(content.strip()):
                            content = text
                            break
                except:
                    continue
            
            # 如果没有找到特定内容区域，获取整个body的文本
            if not content or len(content.strip()) < 100:
                body = self.page('tag:body')
                if body:
                    content = body.text
            
            # 清理文本
            content = self._clean_text(content)
            
            return content
            
        except Exception as e:
            logger.error(f"提取页面内容时发生错误: {str(e)}")
            return ""
    
    def _remove_unwanted_elements(self):
        """移除不需要的页面元素"""
        try:
            # 要移除的元素选择器
            unwanted_selectors = [
                'script',
                'style', 
                'nav',
                'header',
                'footer',
                '.advertisement',
                '.ads',
                '.sidebar',
                '.menu',
                '.navigation',
                '.social-share',
                '.comments'
            ]
            
            for selector in unwanted_selectors:
                try:
                    elements = self.page.eles(selector)
                    for element in elements:
                        element.remove()
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"移除不需要元素时发生错误: {str(e)}")
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        # 移除重复的换行符
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()
    
    def _reset_page(self):
        """重置页面实例"""
        try:
            if self.page:
                self.page.quit()
        except:
            pass
        finally:
            self.page = None
    
    def close(self):
        """关闭浏览器页面"""
        self._reset_page()
        logger.debug("浏览器页面已关闭")
    
    def __del__(self):
        """析构函数，确保资源被释放"""
        self.close()
