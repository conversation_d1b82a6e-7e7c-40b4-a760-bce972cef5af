2025-07-02 11:05:10,022 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:05:10,023 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: URL列表不能为空
2025-07-02 11:05:10,023 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:05:10,023 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 无效的URL格式: invalid-url
2025-07-02 11:05:10,024 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:05:10,024 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 一次最多只能分析10个URL
2025-07-02 11:05:10,364 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:05:10,364 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 请求数据不能为空
2025-07-02 11:05:10,365 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:05:10,365 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 搜索关键词不能为空
2025-07-02 11:05:10,365 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:05:10,365 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 搜索关键词长度不能超过200个字符
2025-07-02 11:10:08,120 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:10:08,121 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: URL列表不能为空
2025-07-02 11:10:08,121 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:10:08,121 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 无效的URL格式: invalid-url
2025-07-02 11:10:08,122 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:10:08,122 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 一次最多只能分析10个URL
2025-07-02 11:10:08,143 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:10:08,143 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 请求数据不能为空
2025-07-02 11:10:08,143 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:10:08,143 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 搜索关键词不能为空
2025-07-02 11:10:08,143 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:10:08,143 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 搜索关键词长度不能超过200个字符
2025-07-02 11:11:50,536 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 11:11:50,536 - app.routes - INFO - 开始搜索: "ELN 719" site:www.elasmogen.com
2025-07-02 11:11:50,536 - app.services.search_service - INFO - 开始搜索: "ELN 719" site:www.elasmogen.com
2025-07-02 11:11:52,982 - app.services.search_service - INFO - 搜索成功: "ELN 719" site:www.elasmogen.com, 返回 0 个结果
2025-07-02 11:11:52,983 - app.routes - ERROR - 搜索服务错误: 搜索未返回结果，请尝试其他关键词
2025-07-02 11:11:52,984 - app.utils.error_handler - ERROR - API错误 [SEARCH_ERROR]: 搜索失败: 搜索未返回结果，请尝试其他关键词
2025-07-02 11:16:26,323 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 11:16:26,324 - app.routes - INFO - 开始搜索: "ELN 719" site:www.elasmogen.com
2025-07-02 11:16:26,324 - app.services.search_service - INFO - 开始搜索: "ELN 719" site:www.elasmogen.com
2025-07-02 11:16:28,832 - app.services.search_service - INFO - 搜索成功: "ELN 719" site:www.elasmogen.com, 返回 0 个结果
2025-07-02 11:16:28,833 - app.routes - ERROR - 搜索服务错误: 搜索未返回结果，请尝试其他关键词
2025-07-02 11:16:28,833 - app.utils.error_handler - ERROR - API错误 [SEARCH_ERROR]: 搜索失败: 搜索未返回结果，请尝试其他关键词
2025-07-02 11:17:01,992 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 11:17:01,992 - app.routes - INFO - 开始搜索: "ELN 719" site:www.elasmogen.com
2025-07-02 11:17:01,992 - app.services.search_service - INFO - 开始搜索: "ELN 719" site:www.elasmogen.com
2025-07-02 11:17:04,506 - app.services.search_service - INFO - 搜索成功: "ELN 719" site:www.elasmogen.com, 返回 0 个结果
2025-07-02 11:17:04,506 - app.routes - ERROR - 搜索服务错误: 搜索未返回结果，请尝试其他关键词
2025-07-02 11:17:04,506 - app.utils.error_handler - ERROR - API错误 [SEARCH_ERROR]: 搜索失败: 搜索未返回结果，请尝试其他关键词
2025-07-02 11:19:43,338 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 11:19:43,338 - app.routes - INFO - 开始搜索: "ELN 719" site:www.elasmogen.com
2025-07-02 11:19:43,338 - app.services.search_service - INFO - 开始搜索: "ELN 719" site:www.elasmogen.com
2025-07-02 11:19:45,842 - app.services.search_service - INFO - 搜索成功: "ELN 719" site:www.elasmogen.com, 返回 0 个结果
2025-07-02 11:19:45,842 - app.routes - ERROR - 搜索服务错误: 搜索未返回结果，请尝试其他关键词
2025-07-02 11:19:45,842 - app.utils.error_handler - ERROR - API错误 [SEARCH_ERROR]: 搜索失败: 搜索未返回结果，请尝试其他关键词
2025-07-02 11:20:38,277 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 11:20:38,277 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 11:20:38,277 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 11:20:40,633 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 11:20:40,633 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 11:20:40,633 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 11:20:40,633 - app.services.openai_service - ERROR - 搜索结果分析失败: module 'openai' has no attribute 'error'
2025-07-02 11:20:40,633 - app.routes - ERROR - OpenAI分析错误: AI分析失败: module 'openai' has no attribute 'error'
2025-07-02 11:20:40,635 - app.utils.error_handler - ERROR - API错误 [OPENAI_ERROR]: AI分析失败: AI分析失败: module 'openai' has no attribute 'error'
2025-07-02 11:40:23,974 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:40:23,975 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: URL列表不能为空
2025-07-02 11:40:23,975 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:40:23,975 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 无效的URL格式: invalid-url
2025-07-02 11:40:23,976 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/deep-analysis
2025-07-02 11:40:23,976 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 一次最多只能分析10个URL
2025-07-02 11:40:23,990 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:40:23,991 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 请求数据不能为空
2025-07-02 11:40:23,992 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:40:23,992 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 搜索关键词不能为空
2025-07-02 11:40:23,992 - app.utils.error_handler - INFO - 请求: POST http://localhost/api/search
2025-07-02 11:40:23,992 - app.utils.error_handler - ERROR - API错误 [VALIDATION_ERROR]: 搜索关键词长度不能超过200个字符
2025-07-02 11:40:31,843 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 11:40:31,843 - app.services.search_service - WARNING - 跳过不完整的搜索结果: {'title': '', 'url': 'https://example.com', 'snippet': '摘要'}
2025-07-02 11:40:31,843 - app.services.search_service - WARNING - 跳过无效URL: invalid-url
2025-07-02 11:40:31,843 - app.services.search_service - INFO - 验证后保留 0 个有效结果
2025-07-02 11:40:59,098 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 11:40:59,098 - app.services.search_service - WARNING - 跳过不完整的搜索结果: {'title': '', 'url': 'https://example.com', 'snippet': '摘要'}
2025-07-02 11:40:59,098 - app.services.search_service - WARNING - 跳过无效URL: invalid-url
2025-07-02 11:40:59,098 - app.services.search_service - INFO - 验证后保留 0 个有效结果
2025-07-02 11:40:59,101 - app.services.openai_service - WARNING - AI API速率限制 (尝试 1/3)
2025-07-02 11:41:01,102 - app.services.openai_service - WARNING - AI API速率限制 (尝试 2/3)
2025-07-02 11:41:05,103 - app.services.openai_service - WARNING - AI API速率限制 (尝试 3/3)
2025-07-02 11:41:05,103 - app.services.openai_service - ERROR - AI API调用失败 (尝试 3/3): AI API速率限制，请稍后重试
2025-07-02 12:34:48,133 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:34:48,133 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:34:48,133 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:34:50,669 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:34:50,669 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:34:50,669 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:34:51,473 - app.services.openai_service - ERROR - AI API连接失败 (尝试 1/3)
2025-07-02 12:34:53,026 - app.services.openai_service - ERROR - AI API连接失败 (尝试 2/3)
2025-07-02 12:34:56,519 - app.services.openai_service - ERROR - AI API连接失败 (尝试 3/3)
2025-07-02 12:34:56,519 - app.services.openai_service - ERROR - 搜索结果分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:34:56,519 - app.routes - ERROR - OpenAI分析错误: AI分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:34:56,519 - app.utils.error_handler - ERROR - API错误 [OPENAI_ERROR]: AI分析失败: AI分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:40:57,024 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:40:57,025 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:40:57,025 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:40:59,525 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:40:59,525 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:40:59,525 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:41:00,092 - app.services.openai_service - ERROR - AI API连接失败 (尝试 1/3)
2025-07-02 12:41:01,351 - app.services.openai_service - ERROR - AI API连接失败 (尝试 2/3)
2025-07-02 12:41:05,807 - app.services.openai_service - ERROR - AI API连接失败 (尝试 3/3)
2025-07-02 12:41:05,808 - app.services.openai_service - ERROR - 搜索结果分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:41:05,808 - app.routes - ERROR - OpenAI分析错误: AI分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:41:05,808 - app.utils.error_handler - ERROR - API错误 [OPENAI_ERROR]: AI分析失败: AI分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:43:35,516 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:43:35,516 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:43:35,516 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:43:38,078 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:43:38,078 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:43:38,078 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:43:38,078 - app.services.openai_service - INFO - 请求端点: https://api.newapi.pro/v1/chat/completions
2025-07-02 12:43:38,078 - app.services.openai_service - INFO - 请求模型: gpt-3.5-turbo
2025-07-02 12:43:39,226 - app.services.openai_service - ERROR - AI API连接失败 (尝试 1/3)
2025-07-02 12:43:40,227 - app.services.openai_service - INFO - 请求端点: https://api.newapi.pro/v1/chat/completions
2025-07-02 12:43:40,227 - app.services.openai_service - INFO - 请求模型: gpt-3.5-turbo
2025-07-02 12:43:40,641 - app.services.openai_service - ERROR - AI API连接失败 (尝试 2/3)
2025-07-02 12:43:42,642 - app.services.openai_service - INFO - 请求端点: https://api.newapi.pro/v1/chat/completions
2025-07-02 12:43:42,642 - app.services.openai_service - INFO - 请求模型: gpt-3.5-turbo
2025-07-02 12:43:44,729 - app.services.openai_service - ERROR - AI API连接失败 (尝试 3/3)
2025-07-02 12:43:44,729 - app.services.openai_service - ERROR - 搜索结果分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:43:44,729 - app.routes - ERROR - OpenAI分析错误: AI分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:43:44,729 - app.utils.error_handler - ERROR - API错误 [OPENAI_ERROR]: AI分析失败: AI分析失败: AI API连接失败，请检查网络连接
2025-07-02 12:44:17,920 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:44:17,920 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:44:17,920 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:44:20,317 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:44:20,317 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:44:20,317 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:44:20,318 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:44:20,318 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:44:25,788 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:44:25,788 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:46:04,442 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:46:04,442 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:46:04,442 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:46:06,831 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:46:06,832 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:46:06,832 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:46:06,832 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:46:06,832 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:46:10,830 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:46:10,830 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:46:15,705 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:46:15,705 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:46:15,705 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:46:18,086 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:46:18,086 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:46:18,086 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:46:18,086 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:46:18,086 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:46:22,209 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:46:22,209 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:47:00,672 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:47:00,672 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:47:00,672 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:47:03,049 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:47:03,049 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:47:03,049 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:47:03,049 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:47:03,049 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:47:07,409 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:47:07,410 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:48:32,137 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:48:32,138 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:48:32,138 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:48:34,602 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:48:34,602 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:48:34,602 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:48:34,602 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:48:34,602 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:48:38,116 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:48:38,116 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:49:17,853 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:49:17,853 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:49:17,853 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:49:20,325 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:49:20,326 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:49:20,326 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:49:20,326 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:49:20,326 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:49:24,757 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:49:24,758 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:50:04,374 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:50:04,374 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:50:04,374 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:50:06,635 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:50:06,635 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:50:06,635 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:50:06,635 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:50:06,635 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:50:08,444 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:50:08,444 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:50:40,010 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:50:40,011 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:50:40,011 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:50:42,363 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:50:42,363 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:50:42,363 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:50:42,363 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:50:42,363 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:50:43,895 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:50:43,896 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:51:01,305 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/search
2025-07-02 12:51:01,306 - app.routes - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:51:01,306 - app.services.search_service - INFO - 开始搜索: "WYL-001C" site:wistapharma.com
2025-07-02 12:51:03,615 - app.services.search_service - INFO - 搜索成功: "WYL-001C" site:wistapharma.com, 返回 1 个结果
2025-07-02 12:51:03,616 - app.services.search_service - INFO - 验证后保留 1 个有效结果
2025-07-02 12:51:03,616 - app.services.openai_service - INFO - 开始分析 1 个搜索结果
2025-07-02 12:51:03,616 - app.services.openai_service - INFO - 请求端点: https://01.gs/v1/chat/completions
2025-07-02 12:51:03,616 - app.services.openai_service - INFO - 请求模型: gpt-4o-mini
2025-07-02 12:51:05,144 - app.services.openai_service - INFO - 搜索结果分析完成
2025-07-02 12:51:05,144 - app.routes - INFO - 搜索完成: "WYL-001C" site:wistapharma.com, 结果数量: 1
2025-07-02 12:51:12,295 - app.utils.error_handler - INFO - 请求: POST http://localhost:5000/api/deep-analysis
2025-07-02 12:51:12,296 - app.routes - INFO - 开始深度分析: 1 个URL
2025-07-02 12:51:12,296 - app.services.scraping_service - INFO - 开始抓取网页: https://www.wistapharma.com/index.php?c=category&id=36
