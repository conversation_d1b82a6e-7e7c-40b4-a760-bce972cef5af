["tests/test_app.py::AppTestCase::test_deep_analysis_api_validation", "tests/test_app.py::AppTestCase::test_health_check", "tests/test_app.py::AppTestCase::test_index_page", "tests/test_app.py::AppTestCase::test_search_api_validation", "tests/test_services.py::OpenAIServiceTestCase::test_build_content_analysis_prompt", "tests/test_services.py::OpenAIServiceTestCase::test_build_search_analysis_prompt", "tests/test_services.py::OpenAIServiceTestCase::test_call_openai_api_rate_limit", "tests/test_services.py::OpenAIServiceTestCase::test_call_openai_api_success", "tests/test_services.py::SearchServiceTestCase::test_parse_search_results_with_items_key", "tests/test_services.py::SearchServiceTestCase::test_parse_search_results_with_results_key", "tests/test_services.py::SearchServiceTestCase::test_validate_search_results"]