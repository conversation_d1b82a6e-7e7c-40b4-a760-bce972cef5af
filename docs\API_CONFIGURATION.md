# AI API配置指南

## 概述

AI搜索调研助手已更新为使用第三方AI服务商的API接口，不再依赖OpenAI官方SDK。这样做的好处包括：

- 🔧 **更灵活的配置**：支持多种兼容OpenAI格式的第三方服务商
- 💰 **成本优化**：可选择性价比更高的服务商
- 🚀 **更好的性能**：减少依赖包，提高启动速度
- 🛡️ **更强的控制**：直接HTTP请求，更好的错误处理

## 支持的服务商

### 1. NewAPI Pro (推荐)
- **API地址**：`https://api.newapi.pro/v1`
- **文档**：https://docs.newapi.pro/api/openai-responses
- **特点**：完全兼容OpenAI格式，稳定可靠

### 2. DeepSeek (备选)
- **API地址**：根据服务商提供
- **文档**：https://docs.newapi.pro/api/deepseek-reasoning-chat/
- **特点**：支持推理模式，适合复杂分析

### 3. 其他兼容服务商
只要API格式兼容OpenAI Chat Completions API的服务商都可以使用。

## 配置方法

### 1. 基础配置

在`.env`文件中配置以下参数：

```env
# AI API配置 (第三方服务商)
OPENAI_API_KEY=your_third_party_api_key_here
OPENAI_BASE_URL=https://api.newapi.pro/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_TIMEOUT=60
```

### 2. 配置说明

| 配置项 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `OPENAI_API_KEY` | 第三方服务商的API密钥 | 无 | ✅ |
| `OPENAI_BASE_URL` | API基础URL | `https://api.newapi.pro/v1` | ❌ |
| `OPENAI_MODEL` | 使用的模型名称 | `gpt-3.5-turbo` | ❌ |
| `OPENAI_TIMEOUT` | 请求超时时间(秒) | `60` | ❌ |

### 3. 支持的模型

常见的可用模型：
- `gpt-3.5-turbo` - 快速响应，适合一般分析
- `gpt-4` - 更强能力，适合复杂分析
- `gpt-4-turbo` - 平衡性能和成本
- 其他服务商特有模型

## API请求格式

应用使用标准的OpenAI Chat Completions API格式：

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的AI搜索调研助手，擅长分析和总结各种信息。"
    },
    {
      "role": "user",
      "content": "用户的提示词内容"
    }
  ],
  "max_tokens": 2000,
  "temperature": 0.7,
  "stream": false
}
```

## 错误处理

应用包含完善的错误处理机制：

### HTTP状态码处理
- `200` - 成功
- `400` - 请求参数错误
- `401` - API密钥无效
- `429` - 速率限制
- `500` - 服务器错误

### 重试机制
- 自动重试失败的请求
- 指数退避算法
- 可配置最大重试次数

### 错误日志
所有API调用错误都会记录到日志文件中，便于调试。

## 性能优化

### 1. 超时设置
```env
OPENAI_TIMEOUT=60  # 根据网络情况调整
```

### 2. 重试配置
```env
MAX_RETRIES=3      # 最大重试次数
REQUEST_DELAY=1    # 重试间隔(秒)
```

### 3. 内容长度控制
应用自动截断过长的内容以避免超出token限制：
- 搜索结果分析：自动格式化
- 网页内容分析：最大8000字符

## 故障排除

### 1. API密钥问题
```
错误：AI API认证失败，请检查API密钥
解决：确认API密钥正确且有效
```

### 2. 网络连接问题
```
错误：AI API连接失败，请检查网络连接
解决：检查网络连接和防火墙设置
```

### 3. 速率限制
```
错误：AI API速率限制，请稍后重试
解决：等待一段时间后重试，或升级API套餐
```

### 4. 模型不支持
```
错误：请求参数错误
解决：检查模型名称是否正确，确认服务商支持该模型
```

## 迁移指南

### 从OpenAI官方API迁移

1. **更新配置**：
   ```env
   # 旧配置
   OPENAI_BASE_URL=https://api.openai.com/v1
   
   # 新配置
   OPENAI_BASE_URL=https://api.newapi.pro/v1
   ```

2. **更新API密钥**：
   使用第三方服务商提供的API密钥

3. **测试功能**：
   启动应用并测试搜索分析功能

### 从其他服务商迁移

只需更新`OPENAI_BASE_URL`和`OPENAI_API_KEY`即可。

## 开发者信息

### API调用代码示例

```python
import requests

def call_ai_api(prompt, api_key, base_url, model):
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": "系统提示词"},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 2000,
        "temperature": 0.7
    }
    
    response = requests.post(
        f"{base_url}/chat/completions",
        headers=headers,
        json=payload,
        timeout=60
    )
    
    return response.json()
```

### 自定义服务商集成

如需集成其他服务商，只需确保：
1. API格式兼容OpenAI Chat Completions
2. 返回格式包含`choices[0].message.content`
3. 支持标准HTTP状态码

## 安全建议

1. **API密钥安全**：
   - 不要在代码中硬编码API密钥
   - 使用环境变量存储敏感信息
   - 定期轮换API密钥

2. **网络安全**：
   - 使用HTTPS传输
   - 配置适当的防火墙规则
   - 监控API使用情况

3. **成本控制**：
   - 设置API使用限额
   - 监控token消耗
   - 优化提示词长度

## 技术支持

如遇到API相关问题：
1. 查看应用日志：`logs/app_*.log`
2. 检查配置文件：`.env`
3. 参考故障排除指南
4. 联系服务商技术支持

---

**更新日期**：2025年7月2日  
**版本**：v2.0 - 第三方API集成版本
