# 🤖 AI搜索调研助手

> 基于Python + Bootstrap的智能搜索调研Web应用，集成Google搜索API和OpenAI API，提供智能搜索分析和深度网页内容分析功能。

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-3.0.0-green.svg)](https://flask.palletsprojects.com/)
[![Bootstrap](https://img.shields.io/badge/Bootstrap-5.3.0-purple.svg)](https://getbootstrap.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## ✨ 功能特性

### 🔍 核心功能
- **智能搜索**：集成Google搜索API，支持关键词搜索
- **AI分析**：使用OpenAI API对搜索结果进行智能分析和总结
- **深度分析**：可选的网页内容深度抓取和二次AI分析
- **自定义提示词**：支持用户自定义AI分析方向

### 🎯 主要应用场景
1. **特定特征页面搜索**：如医药公司的药品pipeline页面搜索和分析
2. **关键词匹配页面搜索**：如投资者关系页面搜索，自动生成数据采集规则
3. **竞品调研**：批量分析竞争对手网站内容
4. **市场研究**：收集和分析行业相关信息

## 🚀 快速开始

### 方法一：使用自动设置脚本（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd ai_research_tool

# 2. 运行自动设置脚本
python setup.py

# 3. 按照提示配置API密钥
# 编辑 .env 文件，填入您的API密钥

# 4. 启动应用
python start.py
```

### 方法二：手动安装

```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 5. 启动应用
python app.py
```

## ⚙️ 配置说明

### 必需配置
在`.env`文件中配置以下必需参数：

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here

# Google搜索API配置
GOOGLE_SEARCH_API_URL=http://************:18183/search
```

### 可选配置
```env
# Flask应用配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here

# 网页抓取配置
SCRAPING_TIMEOUT=30
MAX_RETRIES=3
REQUEST_DELAY=1
```

## 📖 使用指南

### 基础搜索
1. 在首页输入搜索关键词
2. 可选择添加自定义分析提示词
3. 点击"开始搜索分析"
4. 查看搜索结果和AI分析报告

### 深度分析
1. 在搜索表单中勾选"启用深度分析"
2. 完成基础搜索后，选择要深度分析的URL
3. 点击"开始深度分析"
4. 等待网页抓取和AI分析完成
5. 查看详细的深度分析报告

### 自定义提示词示例
- **医药研发分析**：`重点关注药品研发管线信息，提取公司名称、药品名称、研发阶段等关键信息`
- **投资者关系分析**：`识别投资者关系页面，提取财务数据、新闻发布、投资者联系方式等信息`
- **技术文档分析**：`分析技术文档内容，提取API接口、技术规格、使用方法等技术信息`

## 🏗️ 技术架构

### 后端技术栈
- **框架**：Flask 3.0.0
- **网页抓取**：DrissionPage *******
- **AI服务**：OpenAI API
- **HTTP请求**：requests
- **配置管理**：python-dotenv

### 前端技术栈
- **UI框架**：Bootstrap 5.3.0
- **图标**：Font Awesome 6.0.0
- **JavaScript**：原生JS + jQuery 3.6.0

## 📁 项目结构

```
ai_research_tool/
├── app/                    # 应用程序主目录
│   ├── __init__.py        # 应用工厂
│   ├── routes.py          # 路由定义
│   ├── services/          # 服务层
│   └── utils/             # 工具模块
├── static/                # 静态文件
├── templates/             # HTML模板
├── tests/                 # 测试文件
├── docs/                  # 文档目录
├── logs/                  # 日志文件
├── config.py             # 配置管理
├── app.py                # 应用入口
├── start.py              # 启动脚本
├── setup.py              # 设置脚本
├── requirements.txt      # 依赖列表
└── .env.example         # 配置模板
```

## 🧪 测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试文件
python -m pytest tests/test_app.py -v

# 运行测试并显示覆盖率
python -m pytest tests/ --cov=app
```

## 🚀 部署

### 开发环境
```bash
python app.py
```

### 生产环境
```bash
# 使用Gunicorn
pip install gunicorn
gunicorn -c gunicorn.conf.py app:app

# 使用Docker
docker-compose up -d
```

详细部署说明请查看 [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)

## 📚 文档

- [使用说明](docs/README.md) - 详细的使用指南
- [部署指南](docs/DEPLOYMENT.md) - 完整的部署说明
- [项目总结](docs/PROJECT_SUMMARY.md) - 项目功能和技术总结

## 🔧 API接口

### 搜索接口
```http
POST /api/search
Content-Type: application/json

{
    "keywords": "搜索关键词",
    "custom_prompt": "自定义分析提示词（可选）"
}
```

### 深度分析接口
```http
POST /api/deep-analysis
Content-Type: application/json

{
    "urls": ["https://example1.com", "https://example2.com"],
    "custom_prompt": "自定义分析提示词（可选）"
}
```

### 健康检查接口
```http
GET /api/health
```

## 🛠️ 故障排除

### 常见问题

1. **应用无法启动**
   - 检查Python版本（需要3.8+）
   - 确认虚拟环境已激活
   - 验证.env配置文件

2. **API调用失败**
   - 检查OpenAI API密钥是否正确
   - 确认网络连接正常
   - 检查API配额是否充足

3. **网页抓取失败**
   - 确认Chrome浏览器已安装
   - 检查目标网站访问权限
   - 调整超时设置

更多故障排除信息请查看 [docs/README.md](docs/README.md#故障排除)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Flask](https://flask.palletsprojects.com/) - Web框架
- [OpenAI](https://openai.com/) - AI服务
- [DrissionPage](https://github.com/g1879/DrissionPage) - 网页抓取
- [Bootstrap](https://getbootstrap.com/) - UI框架

---

<div align="center">
  <p>如果这个项目对您有帮助，请给它一个⭐️</p>
  <p>Made with ❤️ by AI Research Tool Team</p>
</div>
