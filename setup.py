#!/usr/bin/env python
"""
AI搜索调研助手快速设置脚本
自动完成环境配置和依赖安装
"""
import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                   AI搜索调研助手                              ║
    ║                  快速设置向导                                ║
    ║                                                              ║
    ║  🤖 智能搜索 + AI分析 + 深度调研                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本符合要求: {sys.version}")
    return True

def create_virtual_environment():
    """创建虚拟环境"""
    print("\n📦 设置虚拟环境...")
    
    if Path('venv').exists():
        print("✅ 虚拟环境已存在")
        return True
    
    try:
        print("正在创建虚拟环境...")
        subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
        print("✅ 虚拟环境创建成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 虚拟环境创建失败")
        return False

def install_dependencies():
    """安装依赖包"""
    print("\n📚 安装依赖包...")
    
    # 确定pip路径
    if os.name == 'nt':  # Windows
        pip_path = Path('venv/Scripts/pip.exe')
    else:  # Linux/macOS
        pip_path = Path('venv/bin/pip')
    
    if not pip_path.exists():
        print("❌ 找不到pip，请检查虚拟环境")
        return False
    
    try:
        print("正在安装依赖包...")
        subprocess.run([str(pip_path), 'install', '-r', 'requirements.txt'], check=True)
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖包安装失败")
        return False

def setup_configuration():
    """设置配置文件"""
    print("\n⚙️  设置配置文件...")
    
    if Path('.env').exists():
        print("✅ .env文件已存在")
        return True
    
    if not Path('.env.example').exists():
        print("❌ 找不到.env.example文件")
        return False
    
    try:
        # 复制配置模板
        with open('.env.example', 'r', encoding='utf-8') as src:
            content = src.read()
        
        with open('.env', 'w', encoding='utf-8') as dst:
            dst.write(content)
        
        print("✅ .env文件创建成功")
        print("⚠️  请编辑.env文件，配置您的API密钥:")
        print("   - OPENAI_API_KEY: 您的OpenAI API密钥")
        print("   - GOOGLE_SEARCH_API_URL: Google搜索API地址")
        return True
    except Exception as e:
        print(f"❌ 配置文件创建失败: {e}")
        return False

def create_directories():
    """创建必要目录"""
    print("\n📁 创建项目目录...")
    
    directories = [
        'logs',
        'static/uploads',
        'tests/__pycache__',
        'app/__pycache__'
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✅ {directory}")
        except Exception as e:
            print(f"⚠️  {directory}: {e}")

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*60)
    print("🎉 设置完成！")
    print("="*60)
    print("\n📋 后续步骤:")
    print("1. 编辑 .env 文件，配置您的API密钥")
    print("2. 激活虚拟环境:")
    
    if os.name == 'nt':  # Windows
        print("   venv\\Scripts\\activate")
    else:  # Linux/macOS
        print("   source venv/bin/activate")
    
    print("3. 启动应用:")
    print("   python start.py")
    print("   或者")
    print("   python app.py")
    
    print("\n🌐 应用地址: http://localhost:5000")
    print("\n📖 更多信息请查看:")
    print("   - docs/README.md - 使用说明")
    print("   - docs/DEPLOYMENT.md - 部署指南")
    print("   - docs/PROJECT_SUMMARY.md - 项目总结")

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 创建虚拟环境
    if not create_virtual_environment():
        return
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 设置配置
    if not setup_configuration():
        return
    
    # 创建目录
    create_directories()
    
    # 显示后续步骤
    show_next_steps()

if __name__ == '__main__':
    main()
